<template>
  <div class="client-details-container">
    <!-- 系统概览卡片 -->
    <div class="system-overview-card">
      <div class="system-header">
        <div class="system-icon">
          <WindowsOutlined v-if="systemInfo.os === 'windows'" />
          <AppleOutlined v-else-if="systemInfo.os === 'darwin'" />
          <div v-else class="linux-icon">🐧</div>
        </div>
        <div class="system-title">
          <h2>{{ getSystemName(systemInfo.os) }}</h2>
          <p>{{ systemInfo.arch }} 架构</p>
        </div>
        <div class="status-indicator" :class="{ online: isOnline, offline: !isOnline }">
          <div class="status-dot"></div>
          <span>{{ isOnline ? '在线' : '离线' }}</span>
        </div>
      </div>
      
      <div class="system-metrics">
        <div class="metric-item cpu">
          <div class="metric-icon">🖥️</div>
          <div class="metric-content">
            <div class="metric-label">CPU 使用率</div>
            <div class="metric-value">{{ systemInfo.cpu_usage?.toFixed(1) || 0 }}%</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: (systemInfo.cpu_usage || 0) + '%' }"></div>
            </div>
          </div>
        </div>
        
        <div class="metric-item memory">
          <div class="metric-icon">💾</div>
          <div class="metric-content">
            <div class="metric-label">内存使用率</div>
            <div class="metric-value">{{ systemInfo.memory_usage?.toFixed(1) || 0 }}%</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: (systemInfo.memory_usage || 0) + '%' }"></div>
            </div>
          </div>
        </div>
        
        <div class="metric-item disk">
          <div class="metric-icon">💿</div>
          <div class="metric-content">
            <div class="metric-label">磁盘使用率</div>
            <div class="metric-value">{{ systemInfo.disk_usage?.toFixed(1) || 0 }}%</div>
            <div class="metric-bar">
              <div class="metric-fill" :style="{ width: (systemInfo.disk_usage || 0) + '%' }"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 网络状态卡片 -->
    <div class="network-status-card">
      <div class="card-header">
        <div class="header-left">
          <GlobalOutlined />
          <h3>网络状态</h3>
        </div>
      </div>
      <div class="network-info">
        <div class="network-item">
          <span class="label">本地IP:</span>
          <span class="value">{{ networkInfo.local_ip || '未知' }}</span>
        </div>
        <div class="network-item">
          <span class="label">公网IP:</span>
          <span class="value">{{ networkInfo.public_ip || '未获取' }}</span>
        </div>
        <div class="network-item">
          <span class="label">延迟:</span>
          <span class="value">{{ networkInfo.latency || 0 }}ms</span>
        </div>
        <div class="network-item">
          <span class="label">丢包率:</span>
          <span class="value">{{ networkInfo.packet_loss?.toFixed(2) || 0 }}%</span>
        </div>
      </div>
    </div>

    <!-- 地理位置信息卡片 -->
    <div class="geo-location-card">
      <div class="card-header">
        <div class="header-left">
          <EnvironmentOutlined />
          <h3>地理位置</h3>
        </div>
      </div>
      <div class="geo-info">
        <div class="geo-item">
          <span class="label">国家:</span>
          <span class="value">
            <CountryFlag
              v-if="geoLocation.country"
              :country="geoLocation.country"
              :show-name="true"
              size="medium"
            />
            <span v-else class="unknown">未知</span>
          </span>
        </div>
        <div class="geo-item">
          <span class="label">省份/州:</span>
          <span class="value">{{ geoLocation.province || '未知' }}</span>
        </div>
        <div class="geo-item">
          <span class="label">城市:</span>
          <span class="value">{{ geoLocation.city || '未知' }}</span>
        </div>
        <div class="geo-item">
          <span class="label">ISP:</span>
          <span class="value">{{ geoLocation.isp || '未知' }}</span>
        </div>
        <div class="geo-item">
          <span class="label">ASN:</span>
          <span class="value">{{ geoLocation.asn || '未知' }}</span>
        </div>
        <div class="geo-item">
          <span class="label">数据源:</span>
          <span class="value">{{ geoLocation.source || '未知' }}</span>
        </div>
      </div>
    </div>

    <!-- 地理位置地图 -->
    <div class="geo-map-card">
      <div class="card-header">
        <div class="header-left">
          <EnvironmentOutlined />
          <h3>地理位置地图</h3>
        </div>
        <div class="map-controls">
          <a-button size="small" @click="centerMap" :disabled="!geoLocation.country">
            <template #icon><EnvironmentOutlined /></template>
            定位到客户端
          </a-button>
          <a-button size="small" @click="refreshMap">
            <template #icon><ReloadOutlined /></template>
            刷新地图
          </a-button>
        </div>
      </div>
      <div class="map-container">
        <div class="location-info" v-if="geoLocation.country">
          <div class="location-summary">
            <CountryFlag
              :country="geoLocation.country"
              :show-name="true"
              size="large"
            />
            <span class="location-text">
              <span v-if="geoLocation.province"> - {{ geoLocation.province }}</span>
              <span v-if="geoLocation.city"> - {{ geoLocation.city }}</span>
            </span>
          </div>
          <div class="location-details">
            <span v-if="geoLocation.isp" class="isp-info">ISP: {{ geoLocation.isp }}</span>
            <span v-if="geoLocation.asn" class="asn-info">ASN: {{ geoLocation.asn }}</span>
          </div>
        </div>
        <div class="map-wrapper">
          <GeoMap
            :country="geoLocation.country"
            :province="geoLocation.province"
            :city="geoLocation.city"
            height="250px"
          />
        </div>
      </div>
    </div>

    <!-- 心跳配置卡片 -->
    <div class="heartbeat-config-card">
      <div class="card-header">
        <div class="header-left">
          <HeartOutlined />
          <h3>心跳配置</h3>
        </div>
        <a-button
          type="primary"
          size="small"
          @click="openHeartbeatConfigModal"
          :loading="configUpdateLoading"
        >
          <template #icon><SettingOutlined /></template>
          配置管理
        </a-button>
      </div>
      <div class="config-grid">
        <div class="config-item">
          <div class="config-label">间隔</div>
          <div class="config-value">{{ heartbeatConfig.interval || 30 }}秒</div>
        </div>
        <div class="config-item">
          <div class="config-label">超时</div>
          <div class="config-value">{{ heartbeatConfig.timeout || 10 }}秒</div>
        </div>
        <div class="config-item">
          <div class="config-label">重试次数</div>
          <div class="config-value">{{ heartbeatConfig.max_retries || 5 }}次</div>
        </div>
        <div class="config-item">
          <div class="config-label">抖动范围</div>
          <div class="config-value">{{ heartbeatConfig.jitter_range || 5000 }}ms</div>
        </div>
      </div>
    </div>

    <!-- 客户端管理卡片 -->
    <div class="client-management-card">
      <div class="card-header">
        <div class="header-left">
          <SettingOutlined />
          <h3>客户端管理</h3>
        </div>
      </div>
      <div class="management-actions">
        <div class="action-group">
          <a-button 
            type="primary" 
            :loading="reconnectLoading"
            @click="handleReconnect"
          >
            <template #icon><ReloadOutlined /></template>
            强制重连
          </a-button>
          <a-button
            :loading="remarkUpdateLoading"
            @click="openRemarkModal"
          >
            <template #icon><EditOutlined /></template>
            修改备注
          </a-button>
        </div>

        <!-- 客户端备注显示 -->
        <div class="client-remark-section" v-if="clientInfo && clientInfo.remark">
          <div class="remark-header">
            <span class="remark-title">📝 客户端备注</span>
          </div>
          <div class="remark-content">
            <div class="markdown-content" v-html="renderedRemark"></div>
          </div>
        </div>

        <div class="server-migration" v-if="clientManagement.should_reconnect">
          <a-alert
            message="服务器迁移通知"
            :description="`客户端将重连到: ${clientManagement.new_server_addr}`"
            type="warning"
            show-icon
          />
        </div>
        
        <div class="pending-commands" v-if="clientManagement.commands?.length">
          <h4>待执行命令 ({{ clientManagement.commands.length }})</h4>
          <div class="command-list">
            <div 
              v-for="(command, index) in clientManagement.commands" 
              :key="index"
              class="command-item"
            >
              <code>{{ command }}</code>
              <a-button size="small" type="link" @click="executeCommand(command, index)">
                执行
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时统计卡片 -->
    <div class="realtime-stats-card">
      <div class="card-header">
        <DashboardOutlined />
        <h3>实时统计</h3>
      </div>
      <div class="stats-content">
        <div class="stat-item">
          <div class="stat-icon">⏱️</div>
          <div class="stat-info">
            <div class="stat-label">运行时间</div>
            <div class="stat-value">{{ formatUptime(systemInfo.uptime) }}</div>
          </div>
        </div>
        <div class="stat-item" v-if="systemInfo.load_avg !== undefined">
          <div class="stat-icon">📊</div>
          <div class="stat-info">
            <div class="stat-label">系统负载</div>
            <div class="stat-value">{{ systemInfo.load_avg?.toFixed(2) || 0 }}</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">🔄</div>
          <div class="stat-info">
            <div class="stat-label">最后心跳</div>
            <div class="stat-value">{{ formatLastHeartbeat(lastHeartbeatTime) }}</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">🆔</div>
          <div class="stat-info">
            <div class="stat-label">客户端ID</div>
            <div class="stat-value">{{ clientId }}</div>
          </div>
        </div>
        <div class="stat-item" v-if="connectionTime">
          <div class="stat-icon">🔗</div>
          <div class="stat-info">
            <div class="stat-label">连接时间</div>
            <div class="stat-value">{{ formatConnectionTime(connectionTime) }}</div>
          </div>
        </div>
        <div class="stat-item">
          <div class="stat-icon">📡</div>
          <div class="stat-info">
            <div class="stat-label">在线状态</div>
            <div class="stat-value" :class="{ 'online': isOnline, 'offline': !isOnline }">
              {{ isOnline ? '在线' : '离线' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 心跳配置管理模态框 -->
  <a-modal
    v-model:open="showHeartbeatConfigModal"
    title="心跳配置管理"
    width="600px"
    :confirm-loading="configUpdateLoading"
    @ok="handleUpdateHeartbeatConfig"
    @cancel="handleCancelHeartbeatConfig"
  >
    <a-form
      :model="heartbeatConfigForm"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="心跳间隔" name="interval">
        <a-input-number
          v-model:value="heartbeatConfigForm.interval"
          :min="5"
          :max="300"
          addon-after="秒"
          style="width: 100%"
          placeholder="5-300秒"
        />
        <div class="form-help">客户端发送心跳的时间间隔</div>
      </a-form-item>

      <a-form-item label="超时时间" name="timeout">
        <a-input-number
          v-model:value="heartbeatConfigForm.timeout"
          :min="1"
          :max="60"
          addon-after="秒"
          style="width: 100%"
          placeholder="1-60秒"
        />
        <div class="form-help">等待心跳响应的超时时间</div>
      </a-form-item>

      <a-form-item label="最大重试次数" name="max_retries">
        <a-input-number
          v-model:value="heartbeatConfigForm.max_retries"
          :min="1"
          :max="20"
          addon-after="次"
          style="width: 100%"
          placeholder="1-20次"
        />
        <div class="form-help">心跳失败后的最大重试次数</div>
      </a-form-item>

      <a-form-item label="抖动范围" name="jitter_range">
        <a-input-number
          v-model:value="heartbeatConfigForm.jitter_range"
          :min="0"
          :max="30000"
          addon-after="毫秒"
          style="width: 100%"
          placeholder="0-30000毫秒"
        />
        <div class="form-help">心跳间隔的随机抖动范围，避免同时发送</div>
      </a-form-item>

      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="heartbeatConfigForm.remark"
          :rows="3"
          placeholder="配置备注信息"
        />
      </a-form-item>
    </a-form>
  </a-modal>

  <!-- 修改备注模态框 -->
  <a-modal
    v-model:open="showRemarkModal"
    title="修改客户端备注"
    width="700px"
    :confirm-loading="remarkUpdateLoading"
    @ok="handleUpdateRemark"
    @cancel="handleCancelRemark"
  >
    <a-form
      :model="remarkForm"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item label="备注内容" name="remark">
        <a-textarea
          v-model:value="remarkForm.remark"
          :rows="10"
          placeholder="支持Markdown格式，例如：
# 标题
## 子标题
- 列表项
- 另一个列表项
**粗体文字**
*斜体文字*
`代码`
```
代码块
```"
          style="font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', monospace;"
        />
        <div class="form-help">支持Markdown语法，保存后将以格式化形式显示</div>
      </a-form-item>
    </a-form>

    <!-- 实时预览 -->
    <div class="preview-section">
      <div class="preview-header">📖 实时预览</div>
      <div class="preview-content">
        <div class="markdown-content" v-html="previewRemark"></div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import { getApiBaseUrl } from '@/utils/serverConfig';
import { networkDataStore, updateNetworkStats, clearTrafficData as clearSharedTrafficData, addTestTrafficData, formatBytes, formatSpeed } from '@/stores/networkData';
import {
  WindowsOutlined,
  AppleOutlined,
  GlobalOutlined,
  EnvironmentOutlined,
  HeartOutlined,
  SettingOutlined,
  DashboardOutlined,
  ReloadOutlined,
  EditOutlined,
  ClearOutlined
} from '@ant-design/icons-vue';
import { clientApi } from '@/api';
import CountryFlag from '@/components/common/CountryFlag.vue';
import GeoMap from '@/components/common/GeoMap.vue';

// 接收父组件传递的属性
const props = defineProps({
  clientId: {
    type: [String, Number],
    required: true
  },
  clientInfo: {
    type: Object,
    default: () => ({})
  },
  active: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['refresh']);

// Markdown渲染函数
const renderMarkdown = (text) => {
  if (!text) return '';

  return text
    // 标题
    .replace(/^### (.*$)/gim, '<h3>$1</h3>')
    .replace(/^## (.*$)/gim, '<h2>$1</h2>')
    .replace(/^# (.*$)/gim, '<h1>$1</h1>')
    // 粗体和斜体
    .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
    .replace(/\*(.*)\*/gim, '<em>$1</em>')
    // 代码
    .replace(/`([^`]+)`/gim, '<code>$1</code>')
    // 代码块
    .replace(/```([\s\S]*?)```/gim, '<pre><code>$1</code></pre>')
    // 列表
    .replace(/^\- (.*$)/gim, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/gims, '<ul>$1</ul>')
    // 换行
    .replace(/\n/gim, '<br>');
};

// 计算属性
const renderedRemark = computed(() => renderMarkdown(props.clientInfo?.remark || ''));
const previewRemark = computed(() => renderMarkdown(remarkForm.remark));

// 响应式数据
const systemInfo = reactive({
  os: 'unknown',
  arch: 'unknown',
  cpu_usage: 0,
  memory_usage: 0,
  disk_usage: 0,
  uptime: 0,
  load_avg: 0
});

const networkInfo = reactive({
  local_ip: '',
  public_ip: '',
  latency: 0,
  packet_loss: 0,
  bandwidth: 0,
  total_bytes_recv: 0,
  total_bytes_sent: 0
});

// 地理位置信息
const geoLocation = reactive({
  country: '',
  province: '',
  city: '',
  isp: '',
  asn: '',
  source: ''
});

// 网络流量趋势数据 - 使用共享数据
const trafficChart = ref(null);

const heartbeatConfig = reactive({
  interval: 30,
  timeout: 10,
  max_retries: 5,
  jitter_range: 5000
});

// 心跳配置管理
const showHeartbeatConfigModal = ref(false);
const heartbeatConfigForm = reactive({
  id: null,
  client_id: null,
  interval: 30,
  timeout: 10,
  max_retries: 5,
  jitter_range: 5000,
  remark: ''
});

const clientManagement = reactive({
  should_reconnect: false,
  new_server_addr: '',
  config_update: false,
  commands: []
});

const lastHeartbeatTime = ref(null);
const isOnline = ref(false);
const reconnectLoading = ref(false);
const configUpdateLoading = ref(false);
const connectionTime = ref(null);

// 备注管理
const showRemarkModal = ref(false);
const remarkUpdateLoading = ref(false);
const remarkForm = reactive({
  remark: ''
});

// SSE连接
let eventSource = null;

// 计算属性
const getSystemName = (os) => {
  switch (os) {
    case 'windows': return 'Windows';
    case 'linux': return 'Linux';
    case 'darwin': return 'macOS';
    default: return '未知系统';
  }
};



// 格式化运行时间
const formatUptime = (uptime) => {
  if (!uptime) return '未知';
  const days = Math.floor(uptime / 86400);
  const hours = Math.floor((uptime % 86400) / 3600);
  const minutes = Math.floor((uptime % 3600) / 60);
  return `${days}天 ${hours}小时 ${minutes}分钟`;
};

// 格式化最后心跳时间
const formatLastHeartbeat = (time) => {
  if (!time) return '等待连接...';
  const now = new Date();
  const diff = Math.floor((now - time) / 1000);
  if (diff < 5) return '刚刚';
  if (diff < 60) return `${diff}秒前`;
  if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
  return `${Math.floor(diff / 3600)}小时前`;
};

// 格式化带宽
const formatBandwidth = (bandwidth) => {
  if (!bandwidth || bandwidth === 0) return '未测试';
  if (bandwidth < 1) return `${(bandwidth * 1000).toFixed(0)} Kbps`;
  return `${bandwidth.toFixed(1)} Mbps`;
};

// 删除重复的formatBytes和formatSpeed函数，使用从store导入的版本

// 格式化连接时间
const formatConnectionTime = (time) => {
  if (!time) return '未知';
  const date = new Date(time);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 方法
const connectSSE = () => {
  if (eventSource) {
    eventSource.close();
  }

  // 使用正确的后端地址构建SSE URL，并添加token参数
  const baseUrl = getApiBaseUrl();
  const token = localStorage.getItem('token');
  const sseUrl = `${baseUrl}/client/${props.clientId}/heartbeat-stream?token=${encodeURIComponent(token || '')}`;
  console.log('连接SSE:', sseUrl);

  eventSource = new EventSource(sseUrl);
  
  // 监听心跳事件
  eventSource.addEventListener('heartbeat', (event) => {
    try {
      const data = JSON.parse(event.data);
      updateHeartbeatData(data);
    } catch (error) {
      console.error('解析心跳数据失败:', error);
      console.error('原始数据:', event.data);
    }
  });

  // 也监听默认消息事件（兼容性）
  eventSource.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      updateHeartbeatData(data);
    } catch (error) {
      console.error('解析默认数据失败:', error);
      console.error('原始数据:', event.data);
    }
  };
  
  eventSource.onerror = (error) => {
    console.error('SSE连接错误:', error);
    isOnline.value = false;
  };
};

const updateHeartbeatData = (data) => {
  console.log('=== 收到心跳数据 ===', data);

  if (data.system_info) {
    Object.assign(systemInfo, data.system_info);
  }
  if (data.network_info) {
    console.log('收到网络信息:', data.network_info);

    // 更新共享网络数据
    updateNetworkStats(data.network_info);

    Object.assign(networkInfo, data.network_info);

    // 重新绘制图表
    drawTrafficChart();

    console.log('更新后的共享数据:', networkDataStore.stats);
  }
  if (data.config) {
    Object.assign(heartbeatConfig, data.config);
  }
  if (data.client_info) {
    console.log('=== 收到客户端信息 ===', data.client_info);
    console.log('客户端信息字段:', Object.keys(data.client_info));
    console.log('Country字段:', data.client_info.country);
    console.log('Province字段:', data.client_info.province);
    console.log('City字段:', data.client_info.city);
    console.log('ISP字段:', data.client_info.isp);
    console.log('ASN字段:', data.client_info.asn);
    console.log('GeoSource字段:', data.client_info.geo_source);
    console.log('GeoLocation字段:', data.client_info.geo_location);

    // 更新客户端管理数据
    Object.assign(clientManagement, data.client_info);

    // 更新地理位置信息
    const newGeoLocation = {
      country: data.client_info.country || '',
      province: data.client_info.province || '',
      city: data.client_info.city || '',
      isp: data.client_info.isp || '',
      asn: data.client_info.asn || '',
      source: data.client_info.geo_source || ''
    };

    console.log('=== 更新地理位置信息 ===', newGeoLocation);
    Object.assign(geoLocation, newGeoLocation);
    console.log('=== 更新后的地理位置 ===', geoLocation);
  }

  if (data.status) {
    // 更新在线状态和连接时间
    if (data.status.online !== undefined) {
      isOnline.value = data.status.online;
    }
    if (data.status.last_active_at) {
      lastHeartbeatTime.value = new Date(data.status.last_active_at);
    }
    if (data.status.connection_time) {
      connectionTime.value = new Date(data.status.connection_time);
    }
  }

  // 更新最后心跳时间
  if (!lastHeartbeatTime.value) {
    lastHeartbeatTime.value = new Date();
  }

  // 如果收到心跳数据，说明客户端在线
  if (isOnline.value === null || isOnline.value === undefined) {
    isOnline.value = true;
  }
};

const handleReconnect = async () => {
  reconnectLoading.value = true;
  try {
    // 🚀 使用断开连接API实现强制重连（断开后客户端会自动重连）
    await clientApi.disconnectClient({ id: props.clientId });
    message.success('强制重连成功，客户端将自动重新连接');

    // 延迟刷新客户端信息，给客户端重连时间
    setTimeout(() => {
      // 触发父组件刷新客户端信息
      emit('refresh');
    }, 2000);
  } catch (error) {
    console.error('强制重连失败:', error);
    message.error('强制重连失败: ' + (error.response?.data?.error || error.response?.data?.message || error.message));
  } finally {
    reconnectLoading.value = false;
  }
};

const handleConfigUpdate = async () => {
  configUpdateLoading.value = true;
  try {
    // 调用配置更新API
    await clientApi.sendClientConfigUpdate(props.clientId);
    message.success('配置更新指令已发送');
  } catch (error) {
    message.error('发送配置更新指令失败');
  } finally {
    configUpdateLoading.value = false;
  }
};

// 心跳配置管理方法
const openHeartbeatConfigModal = async () => {
  showHeartbeatConfigModal.value = true;
  await loadHeartbeatConfig();
};

const loadHeartbeatConfig = async () => {
  try {
    const result = await clientApi.getHeartbeatConfig(props.clientId);
    const config = result.data;
    Object.assign(heartbeatConfigForm, {
      id: config.id,
      client_id: config.client_id,
      interval: config.interval,
      timeout: config.timeout,
      max_retries: config.max_retries,
      jitter_range: config.jitter_range,
      remark: config.remark || ''
    });
  } catch (error) {
    console.error('加载心跳配置失败:', error);
    message.error('加载心跳配置失败: ' + (error.response?.data?.message || error.message));
    showHeartbeatConfigModal.value = false;
  }
};

const handleUpdateHeartbeatConfig = async () => {
  try {
    configUpdateLoading.value = true;

    if (!heartbeatConfigForm.id) {
      message.error('配置ID不存在，无法更新');
      return;
    }

    await clientApi.updateHeartbeatConfig(heartbeatConfigForm);
    message.success('心跳配置更新成功');
    showHeartbeatConfigModal.value = false;

    // 更新当前显示的配置
    Object.assign(heartbeatConfig, {
      interval: heartbeatConfigForm.interval,
      timeout: heartbeatConfigForm.timeout,
      max_retries: heartbeatConfigForm.max_retries,
      jitter_range: heartbeatConfigForm.jitter_range
    });
  } catch (error) {
    console.error('更新心跳配置失败:', error);
    message.error('更新心跳配置失败: ' + (error.response?.data?.message || error.message));
  } finally {
    configUpdateLoading.value = false;
  }
};

const handleCancelHeartbeatConfig = () => {
  showHeartbeatConfigModal.value = false;
  // 重置表单
  Object.assign(heartbeatConfigForm, {
    id: null,
    client_id: null,
    interval: 30,
    timeout: 10,
    max_retries: 5,
    jitter_range: 5000,
    remark: ''
  });
};

// 备注管理方法
const openRemarkModal = () => {
  remarkForm.remark = props.clientInfo?.remark || '';
  showRemarkModal.value = true;
};

const handleUpdateRemark = async () => {
  try {
    remarkUpdateLoading.value = true;

    const response = await clientApi.updateClientRemark({
      id: props.clientId,
      remark: remarkForm.remark
    });

    message.success('备注更新成功');
    showRemarkModal.value = false;

    // 更新本地显示 - 触发父组件刷新
    emit('refresh');
  } catch (error) {
    console.error('更新备注失败:', error);
    message.error('更新备注失败: ' + (error.response?.data?.message || error.message));
  } finally {
    remarkUpdateLoading.value = false;
  }
};

const handleCancelRemark = () => {
  showRemarkModal.value = false;
  remarkForm.remark = '';
};

// 网络流量相关方法 - 使用共享数据

const drawTrafficChart = () => {
  if (!trafficChart.value) {
    console.log('Canvas元素未找到');
    return;
  }

  const canvas = trafficChart.value;
  const ctx = canvas.getContext('2d');
  const width = canvas.width;
  const height = canvas.height;

  // 使用共享数据的简化引用
  const trafficData = networkDataStore.traffic;

  console.log('绘制图表:', { width, height, dataPoints: trafficData.timestamps.length });

  // 清空画布
  ctx.clearRect(0, 0, width, height);

  // 绘制边框用于调试
  ctx.strokeStyle = '#ddd';
  ctx.lineWidth = 1;
  ctx.strokeRect(0, 0, width, height);

  if (networkDataStore.traffic.timestamps.length === 0) {
    // 显示无数据提示
    ctx.fillStyle = '#999';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('暂无数据，等待网络数据更新...', width / 2, height / 2);
    return;
  }

  // 计算最大值用于缩放
  const maxDownload = Math.max(...networkDataStore.traffic.downloadSpeeds, 1);
  const maxUpload = Math.max(...networkDataStore.traffic.uploadSpeeds, 1);
  const maxSpeed = Math.max(maxDownload, maxUpload);

  // 绘制网格线
  ctx.strokeStyle = '#f0f0f0';
  ctx.lineWidth = 1;

  // 水平网格线
  for (let i = 0; i <= 4; i++) {
    const y = (height - 40) * i / 4 + 20;
    ctx.beginPath();
    ctx.moveTo(40, y);
    ctx.lineTo(width - 20, y);
    ctx.stroke();
  }

  // 垂直网格线
  const dataLength = networkDataStore.traffic.timestamps.length;
  for (let i = 0; i < dataLength; i += Math.ceil(dataLength / 8)) {
    const x = 40 + (width - 60) * i / (dataLength - 1);
    ctx.beginPath();
    ctx.moveTo(x, 20);
    ctx.lineTo(x, height - 20);
    ctx.stroke();
  }

  // 绘制下载速度线
  if (networkDataStore.traffic.downloadSpeeds.length > 1) {
    ctx.strokeStyle = '#1890ff';
    ctx.lineWidth = 2;
    ctx.beginPath();

    for (let i = 0; i < networkDataStore.traffic.downloadSpeeds.length; i++) {
      const x = 40 + (width - 60) * i / (networkDataStore.traffic.downloadSpeeds.length - 1);
      const y = height - 20 - (height - 40) * networkDataStore.traffic.downloadSpeeds[i] / maxSpeed;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();
  }

  // 绘制上传速度线
  if (networkDataStore.traffic.uploadSpeeds.length > 1) {
    ctx.strokeStyle = '#52c41a';
    ctx.lineWidth = 2;
    ctx.beginPath();

    for (let i = 0; i < networkDataStore.traffic.uploadSpeeds.length; i++) {
      const x = 40 + (width - 60) * i / (networkDataStore.traffic.uploadSpeeds.length - 1);
      const y = height - 20 - (height - 40) * networkDataStore.traffic.uploadSpeeds[i] / maxSpeed;

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();
  }

  // 绘制Y轴标签
  ctx.fillStyle = '#666';
  ctx.font = '10px Arial';
  ctx.textAlign = 'right';

  for (let i = 0; i <= 4; i++) {
    const y = (height - 40) * i / 4 + 25;
    const value = maxSpeed * (4 - i) / 4;
    ctx.fillText(formatSpeed(value), 35, y);
  }
};

const clearTrafficData = () => {
  clearSharedTrafficData();
  drawTrafficChart();
};

const addTestData = () => {
  addTestTrafficData();
  drawTrafficChart();
  message.success('已添加测试数据');
};

const executeCommand = async (command, index) => {
  try {
    // 调用命令执行API
    await clientApi.executeClientCommand(props.clientId, command);

    // 从待执行列表中移除
    clientManagement.commands.splice(index, 1);
    message.success('命令执行指令已发送');
  } catch (error) {
    message.error('发送命令执行指令失败');
  }
};

// 地图相关方法
const mapContainer = ref(null);

const centerMap = () => {
  if (!geoLocation.country) {
    message.warning('暂无地理位置信息');
    return;
  }
  message.info(`定位到: ${geoLocation.country} ${geoLocation.province || ''} ${geoLocation.city || ''}`);
};

const refreshMap = () => {
  message.info('地图已刷新');
};

// 生命周期
onMounted(() => {
  if (props.active) {
    connectSSE();
  }
  // 初始化图表
  nextTick(() => {
    drawTrafficChart();
  });

  // 初始化地理位置信息
  if (props.clientInfo) {
    console.log('=== 组件挂载时的clientInfo ===', props.clientInfo);
    console.log('clientInfo字段:', Object.keys(props.clientInfo));
    console.log('初始Country:', props.clientInfo.country);
    console.log('初始Province:', props.clientInfo.province);
    console.log('初始City:', props.clientInfo.city);
    console.log('初始ISP:', props.clientInfo.isp);
    console.log('初始ASN:', props.clientInfo.asn);
    console.log('初始GeoSource:', props.clientInfo.geo_source);

    const initialGeoLocation = {
      country: props.clientInfo.country || '',
      province: props.clientInfo.province || '',
      city: props.clientInfo.city || '',
      isp: props.clientInfo.isp || '',
      asn: props.clientInfo.asn || '',
      source: props.clientInfo.geo_source || ''
    };

    console.log('=== 初始化地理位置信息 ===', initialGeoLocation);
    Object.assign(geoLocation, initialGeoLocation);
    console.log('=== 初始化后的地理位置 ===', geoLocation);
  }
});

onUnmounted(() => {
  if (eventSource) {
    eventSource.close();
  }
});
</script>

<style scoped>
.client-details-container {
  padding: 20px;
  display: grid;
  grid-template-columns: 2fr 1fr;
  grid-template-rows: auto auto auto auto auto;
  gap: 20px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

/* 系统概览卡片 - 占据左上角大区域 */
.system-overview-card {
  grid-column: 1;
  grid-row: 1 / 3;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 30px;
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  position: relative;
  overflow: hidden;
}

.system-overview-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  pointer-events: none;
}

.system-header {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  position: relative;
  z-index: 1;
}

.system-icon {
  font-size: 48px;
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.linux-icon {
  font-size: 40px;
}

.system-title h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
}

.system-title p {
  margin: 5px 0 0 0;
  opacity: 0.8;
  font-size: 14px;
}

.status-indicator {
  margin-left: auto;
  display: flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.status-indicator.online .status-dot {
  background: #52c41a;
  box-shadow: 0 0 10px #52c41a;
}

.status-indicator.offline .status-dot {
  background: #ff4d4f;
  box-shadow: 0 0 10px #ff4d4f;
}

.system-metrics {
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.metric-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.metric-icon {
  font-size: 24px;
  margin-right: 20px;
  width: 40px;
  text-align: center;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 10px;
}

.metric-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 3px;
  transition: width 0.3s ease;
}

/* 网络状态卡片 - 右上角 */
.network-status-card {
  grid-column: 2;
  grid-row: 1;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

/* 地理位置卡片 */
.geo-location-card {
  grid-column: 2;
  grid-row: 2;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  color: #1890ff;
}

.card-header h3 {
  margin: 0 0 0 10px;
  font-size: 16px;
  font-weight: 600;
}

.network-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.network-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.network-item:last-child {
  border-bottom: none;
}

.network-item .label {
  color: #666;
  font-size: 14px;
}

.network-item .value {
  font-weight: 500;
  color: #333;
}

/* 地理位置信息样式 */
.geo-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.geo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.geo-item:last-child {
  border-bottom: none;
}

.geo-item .label {
  color: #666;
  font-size: 14px;
}

.geo-item .value {
  font-weight: 500;
  color: #333;
}

.geo-item .value.unknown {
  color: #999;
  font-style: italic;
}

.country-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 心跳配置卡片 - 右中 */
.heartbeat-config-card {
  grid-column: 2;
  grid-row: 3;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

/* 地理位置地图卡片 */
.geo-map-card {
  grid-column: 1 / -1;
  grid-row: 5;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.map-controls {
  display: flex;
  gap: 8px;
}

.map-container {
  margin-top: 16px;
}

.location-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.location-summary {
  display: flex;
  align-items: center;
  gap: 8px;
}

.location-flag {
  font-size: 20px;
}

.location-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.location-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.isp-info, .asn-info {
  font-size: 12px;
  color: #666;
  background: white;
  padding: 2px 8px;
  border-radius: 4px;
}

.map-wrapper {
  background: #fafafa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e8e8e8;
  min-height: 300px;
}

.map-display {
  width: 100%;
  height: 300px;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.map-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.map-placeholder p {
  margin-top: 16px;
  font-size: 14px;
}

.map-content {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.location-marker {
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: markerPulse 2s infinite;
}

.marker-pin {
  font-size: 32px;
  color: #f5222d;
  margin-bottom: 8px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.marker-info {
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  text-align: center;
  border: 1px solid #e8e8e8;
}

.marker-title {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.marker-subtitle {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

@keyframes markerPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.config-item {
  text-align: center;
  padding: 15px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.config-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.config-value {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 客户端管理卡片 - 左下角大区域 */
.client-management-card {
  grid-column: 1;
  grid-row: 3 / 5;
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.action-group {
  display: flex;
  gap: 12px;
  margin-bottom: 20px;
}

.server-migration {
  margin-bottom: 20px;
}

.pending-commands h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.command-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.command-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.command-item code {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

/* 实时统计卡片 - 右下角 */
.realtime-stats-card {
  grid-column: 2;
  grid-row: 4;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #f0f0f0;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.stat-icon {
  font-size: 20px;
  margin-right: 12px;
  width: 30px;
  text-align: center;
}

.stat-info {
  flex: 1;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.stat-value.online {
  color: #52c41a;
  font-weight: 700;
}

.stat-value.offline {
  color: #ff4d4f;
  font-weight: 700;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .client-details-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto auto auto auto auto;
  }

  .system-overview-card {
    grid-column: 1;
    grid-row: 1;
  }

  .network-status-card {
    grid-column: 1;
    grid-row: 2;
  }

  .geo-location-card {
    grid-column: 1;
    grid-row: 3;
  }

  .heartbeat-config-card {
    grid-column: 1;
    grid-row: 4;
  }

  .client-management-card {
    grid-column: 1;
    grid-row: 5;
  }

  .realtime-stats-card {
    grid-column: 1;
    grid-row: 6;
  }

  .geo-map-card {
    grid-column: 1;
    grid-row: 7;
  }
}

/* 动画效果 */
.metric-fill {
  animation: fillAnimation 1s ease-out;
}

@keyframes fillAnimation {
  from {
    width: 0;
  }
  to {
    width: var(--target-width);
  }
}

.stat-item, .config-item, .network-item {
  transition: all 0.3s ease;
}

.stat-item:hover, .config-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 心跳配置模态框样式 */
.form-help {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-header h3 {
  margin: 0;
}

/* 客户端备注样式 */
.client-remark-section {
  margin-top: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1890ff;
}

.remark-header {
  margin-bottom: 12px;
}

.remark-title {
  font-weight: 600;
  color: #1890ff;
  font-size: 14px;
}

.remark-content {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  position: relative;
}

.markdown-content {
  font-family: 'JetBrains Mono', 'SF Mono', 'Monaco', monospace;
  font-size: 13px;
  line-height: 1.6;
  color: #333;
}

.markdown-content h1 {
  font-size: 18px;
  font-weight: bold;
  margin: 16px 0 8px 0;
  color: #1890ff;
}

.markdown-content h2 {
  font-size: 16px;
  font-weight: bold;
  margin: 14px 0 6px 0;
  color: #1890ff;
}

.markdown-content h3 {
  font-size: 14px;
  font-weight: bold;
  margin: 12px 0 4px 0;
  color: #1890ff;
}

.markdown-content strong {
  font-weight: bold;
  color: #d63384;
}

.markdown-content em {
  font-style: italic;
  color: #6f42c1;
}

.markdown-content code {
  background: #f1f3f4;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'JetBrains Mono', monospace;
  font-size: 12px;
  color: #e83e8c;
}

.markdown-content pre {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #28a745;
  overflow-x: auto;
  margin: 8px 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
  color: #333;
}

.markdown-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.markdown-content li {
  margin: 4px 0;
  list-style-type: disc;
}

/* 备注编辑模态框样式 */
.preview-section {
  margin-top: 16px;
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.preview-header {
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 8px;
  font-size: 14px;
}

.preview-content {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
}
</style>

package tcp

import (
	"server/core/manager"
	"server/global"
	proc2 "server/model/response/proc"
	"server/model/tlv"
	"server/utils"

	"go.uber.org/zap"
)

// handleProcessPacket 处理进程数据包
func (l *TCPListener) handleProcessPacket(remoteAddr string, packet *tlv.Packet) error {
	global.LOG.Info("收到进程数据包", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))

	// 根据Code字段判断进程操作类型
	switch packet.Header.Code {
	case tlv.ProcessList:
		// 处理进程列表响应
		return l.handleProcessOperationResponse(remoteAddr, packet, "list_process")
	case tlv.ProcessKill:
		// 处理进程终止响应
		return l.handleProcessOperationResponse(remoteAddr, packet, "kill_process")
	case tlv.ProcessStart:
		// 处理进程启动响应
		return l.handleProcessOperationResponse(remoteAddr, packet, "start_process")
	case tlv.ProcessDetails:
		// 处理进程详情响应
		return l.handleProcessOperationResponse(remoteAddr, packet, "get_process_details")
	case tlv.ProcessSuspend:
		// 处理进程挂起响应
		return l.handleProcessOperationResponse(remoteAddr, packet, "suspend_process")
	case tlv.ProcessResume:
		// 处理进程恢复响应
		return l.handleProcessOperationResponse(remoteAddr, packet, "resume_process")
	default:
		global.LOG.Warn("未知的进程操作类型", zap.String("remoteAddr", remoteAddr), zap.Uint8("code", packet.Header.Code))
		return nil
	}
}

// handleProcessOperationResponse 处理进程操作响应
func (l *TCPListener) handleProcessOperationResponse(remoteAddr string, packet *tlv.Packet, operation string) error {
	switch operation {
	case "list_process":
		// 反序列化进程列表响应
		var response proc2.ProcessListResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化进程列表响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "list_process", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

		if response.Error != "" {
			global.LOG.Warn("客户端进程列表操作失败",
				zap.String("remoteAddr", remoteAddr),
				zap.String("error", response.Error))
		} else {
			global.LOG.Info("进程列表响应处理成功",
				zap.String("remoteAddr", remoteAddr),
				zap.Int("processCount", len(response.Processes)))
		}

	case "kill_process":
		// 处理进程终止响应
		var response proc2.ProcessKillResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化进程终止响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("进程终止响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Uint32("pid", uint32(response.PID)),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "kill_process", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	case "start_process":
		// 处理进程启动响应
		var response proc2.ProcessStartResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化进程启动响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("进程启动响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Uint32("pid", uint32(response.PID)),
			zap.String("processName", response.Executable),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "start_process", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	case "get_process_details":
		// 处理进程详情响应
		var response proc2.ProcessDetailsResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化进程详情响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		// 检查Process字段是否为nil，防止空指针解引用
		if response.Process == nil {
			global.LOG.Warn("进程详情响应中Process字段为空",
				zap.String("remoteAddr", remoteAddr),
				zap.Bool("success", response.Success),
				zap.String("error", response.Error))

			// 存储响应到缓存
			if response.TaskID > 0 {
				manager.ResponseMgr.StoreResponse(response.TaskID, "get_process_details", response, response.Error)
			} else {
				global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
			}
			return nil
		}

		global.LOG.Info("进程详情响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Uint32("pid", uint32(response.Process.PID)),
			zap.String("processName", response.Process.Name),
			zap.Int("moduleCount", len(response.Modules)),
			zap.Int("connectionCount", len(response.Connections)),
			zap.Int("openFileCount", len(response.OpenFiles)),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "get_process_details", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	case "suspend_process":
		// 处理进程挂起响应
		var response proc2.ProcessSuspendResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化进程挂起响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("进程挂起响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Uint32("pid", uint32(response.PID)),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "suspend_process", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	case "resume_process":
		// 处理进程恢复响应
		var response proc2.ProcessResumeResponse
		if err := utils.SerializerManager.Deserialize(packet.PacketData.Data, &response); err != nil {
			global.LOG.Error("反序列化进程恢复响应失败", zap.Error(err), zap.String("remoteAddr", remoteAddr))
			return err
		}

		global.LOG.Info("进程恢复响应",
			zap.String("remoteAddr", remoteAddr),
			zap.Bool("success", response.Success),
			zap.Uint32("pid", uint32(response.PID)),
			zap.String("error", response.Error))

		// 存储响应到缓存
		if response.TaskID > 0 {
			manager.ResponseMgr.StoreResponse(response.TaskID, "resume_process", response, response.Error)
		} else {
			global.LOG.Warn("响应中缺少TaskID", zap.String("remoteAddr", remoteAddr))
		}

	default:
		global.LOG.Warn("未知的进程操作类型", zap.String("remoteAddr", remoteAddr), zap.String("operation", operation))
	}

	return nil
}

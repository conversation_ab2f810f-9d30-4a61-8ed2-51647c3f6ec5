package manager

import (
	"errors"
	"fmt"
	"server/core/manager/dbpool"
	"server/core/manager/events"
	"server/global"
	"server/model/sys"
	"server/model/tlv"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type ClientManager struct {
	clients map[uint]*sys.Client
	mutex   sync.RWMutex
}

var GlobalClientManager = &ClientManager{
	clients: make(map[uint]*sys.Client),
}

// RegisterClient 注册新客户端
func (c *ClientManager) RegisterClient(listenerID uint, listenerType string, remoteAddr string, OS string, sessionType string) (client sys.Client, err error) {
	var isNewClient bool

	// 🚀 使用数据库连接池进行事务操作，确保客户端注册的原子性
	err = dbpool.ExecuteDBTransaction("client_register", func(db *gorm.DB) error {
		// 检查监听器是否存在
		var listener sys.Listener
		if err := db.Where("id = ?", listenerID).First(&listener).Error; err != nil {
			return errors.New("未找到监听器")
		}

		// 检查是否已存在相同的客户端
		var existingClient sys.Client
		result := db.Where("listener_id = ? AND remote_addr = ?", listenerID, remoteAddr).First(&existingClient)
		if result.Error == nil {
			// 🚨 关键修复：记录客户端之前的状态
			wasOffline := existingClient.Status == 0

			// 客户端已存在，更新状态、时间和详细信息
			existingClient.Status = 1
			existingClient.LastActiveAt = time.Now()
			if err := db.Save(&existingClient).Error; err != nil {
				return err
			}
			client = existingClient

			// 🚨 关键修复：如果客户端之前是离线状态，现在重新上线，应该发送通知
			isNewClient = wasOffline
			return nil
		} else if !errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 发生了其他错误
			return result.Error
		}

		// 创建新客户端
		now := time.Now()
		client = sys.Client{
			ListenerID:   listenerID,
			ListenerType: listenerType,
			RemoteAddr:   remoteAddr,
			OS:           OS,
			SessionType:  sessionType,
			ConnectedAt:  now,
			LastActiveAt: now,
			Status:       1,
			Remark:       "",
			TimeOutCount: 0,
		}

		if err := db.Create(&client).Error; err != nil {
			return err
		}

		isNewClient = true
		return nil
	})

	if err != nil {
		return client, err
	}

	// 添加到内存缓存
	c.mutex.Lock()
	c.clients[client.ID] = &client
	c.mutex.Unlock()

	// 🔔 发布客户端上线事件（新客户端或从离线状态恢复的客户端）
	if isNewClient {
		global.LOG.Info("🚀 发布客户端上线事件",
			zap.Uint("client_id", client.ID),
			zap.String("hostname", client.Hostname),
			zap.String("remote_addr", client.RemoteAddr),
			zap.String("listener_type", listenerType))
		events.PublishClientOnline(&client)
	} else {
		global.LOG.Info("🔄 客户端重复连接（已在线），跳过上线事件",
			zap.Uint("client_id", client.ID),
			zap.String("hostname", client.Hostname))
	}

	return client, nil
}

// UpdateClientStatus 更新客户端状态
func (c *ClientManager) UpdateClientStatus(id uint, status int) (err error) {
	// 🚀 使用数据库连接池异步更新客户端状态
	return dbpool.ExecuteDBOperationAsyncAndWait("client_status_update", func(db *gorm.DB) error {
		var client sys.Client
		if err := db.Where("id = ?", id).First(&client).Error; err != nil {
			return errors.New("未找到客户端")
		}

		// 更新状态
		return db.Model(&client).Updates(map[string]interface{}{
			"status":         status,
			"last_active_at": time.Now(),
		}).Error
	})
}

// UpdateClientOS 更新客户端操作系统信息
func (c *ClientManager) UpdateClientOS(id uint, os string) (err error) {
	// 🚀 使用数据库连接池异步更新客户端OS信息
	return dbpool.ExecuteDBOperationAsyncAndWait("client_os_update", func(db *gorm.DB) error {
		var client sys.Client
		if err := db.Where("id = ?", id).First(&client).Error; err != nil {
			return errors.New("未找到客户端")
		}

		// 更新操作系统信息
		return db.Model(&client).Update("os", os).Error
	})
}

// RegisterClientWithMetadata: 🚀 新增：使用完整METADATA注册客户端
func (c *ClientManager) RegisterClientWithMetadata(listenerID uint, listenerType string, remoteAddr string, metadata *tlv.METADATA) (client sys.Client, err error) {
	var isNewClient bool = true

	// 🚀 检查监听器是否存在
	var listener sys.Listener
	if err = dbpool.ExecuteDBOperationAsyncAndWait("listener_check", func(db *gorm.DB) error {
		return db.Where("id = ?", listenerID).First(&listener).Error
	}); err != nil {
		return client, errors.New("未找到监听器")
	}

	// 🔑 优先基于ClientID查找现有客户端（真正的机器唯一标识）
	var existingClient sys.Client

	if metadata.ClientID != "" {
		// 🚀 首先尝试通过ClientID查找
		err = dbpool.ExecuteDBOperationAsyncAndWait("client_by_id_lookup", func(db *gorm.DB) error {
			if err := db.Where("client_id = ?", metadata.ClientID).First(&existingClient).Error; err != nil {
				return err
			}
			// 🚨 关键修复：记录客户端之前的状态
			wasOffline := existingClient.Status == 0

			// 找到相同机器的客户端，更新所有信息（包括可能变化的RemoteAddr）
			c.updateClientFromMetadata(&existingClient, metadata)
			existingClient.Status = 1
			existingClient.LastActiveAt = time.Now()
			existingClient.RemoteAddr = remoteAddr // 更新远程地址（可能因网络变化而改变）
			existingClient.ListenerID = listenerID // 更新监听器ID（可能重新连接到不同监听器）

			// 🚨 关键修复：如果客户端之前是离线状态，现在重新上线，应该发送通知
			isNewClient = wasOffline

			return db.Save(&existingClient).Error
		})
		if err == nil {
			client = existingClient
			global.LOG.Info(fmt.Sprintf("🔄 客户端重新连接: ClientID=%s, 新地址=%s, 需要通知=%v", metadata.ClientID, remoteAddr, isNewClient))
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			// 发生了其他错误
			return client, err
		}
	}

	// 🚀 如果ClientID查找失败，回退到RemoteAddr查找（兼容旧版本）
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		err = dbpool.ExecuteDBOperationAsyncAndWait("client_by_addr_lookup", func(db *gorm.DB) error {
			if err := db.Where("listener_id = ? AND remote_addr = ?", listenerID, remoteAddr).First(&existingClient).Error; err != nil {
				return err
			}
			// 🚨 关键修复：记录客户端之前的状态
			wasOffline := existingClient.Status == 0

			// 客户端已存在，更新所有信息
			c.updateClientFromMetadata(&existingClient, metadata)
			existingClient.Status = 1
			existingClient.LastActiveAt = time.Now()

			// 🚨 关键修复：如果客户端之前是离线状态，现在重新上线，应该发送通知
			isNewClient = wasOffline

			return db.Save(&existingClient).Error
		})
		if err == nil {
			client = existingClient
			global.LOG.Info(fmt.Sprintf("🔄 客户端重新连接(通过地址): RemoteAddr=%s, 需要通知=%v", remoteAddr, isNewClient))
		} else if !errors.Is(err, gorm.ErrRecordNotFound) {
			// 发生了其他错误
			return client, err
		}
	}

	// 如果没有找到现有客户端，创建新客户端
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		now := time.Now()
		client = sys.Client{
			ListenerID:   listenerID,
			ListenerType: listenerType,
			RemoteAddr:   remoteAddr,
			ConnectedAt:  now,
			LastActiveAt: now,
			Status:       1,
		}

		// 从METADATA填充详细信息
		c.updateClientFromMetadata(&client, metadata)

		// 🚀 保存到数据库
		if err = dbpool.ExecuteDBOperationAsyncAndWait("new_client_create", func(db *gorm.DB) error {
			return db.Create(&client).Error
		}); err != nil {
			return client, err
		}

		// 🚀 为新客户端自动创建心跳配置
		c.createHeartbeatConfigForClient(client.ID)

		isNewClient = true
	} else if err != nil {
		// 发生了其他错误
		return client, err
	}

	// 添加到内存缓存
	c.mutex.Lock()
	c.clients[client.ID] = &client
	c.mutex.Unlock()

	// 🔔 发布客户端上线事件（新客户端或从离线状态恢复的客户端）
	if isNewClient {
		global.LOG.Info("🚀 发布客户端上线事件(WithMetadata)",
			zap.Uint("client_id", client.ID),
			zap.String("hostname", client.Hostname),
			zap.String("remote_addr", client.RemoteAddr),
			zap.String("client_id_metadata", metadata.ClientID))
		events.PublishClientOnline(&client)
	} else {
		global.LOG.Info("🔄 客户端重复连接（已在线），跳过上线事件",
			zap.Uint("client_id", client.ID),
			zap.String("hostname", client.Hostname))
	}

	return client, nil
}

// 🚀 从METADATA更新客户端信息
func (c *ClientManager) updateClientFromMetadata(client *sys.Client, metadata *tlv.METADATA) {
	// 基本信息
	client.OS = metadata.OS
	client.SessionType = metadata.ShellType
	client.Username = metadata.Username
	client.Hostname = metadata.Hostname
	client.Architecture = metadata.Architecture
	client.ClientID = metadata.ClientID

	// 进程信息
	if metadata.Process.Name != "" {
		client.ProcessName = metadata.Process.Name
	}

	// 网络信息
	client.LocalIP = metadata.Network.LocalIP
	client.PublicIP = metadata.Network.PublicIP

	// 🌐 拓扑图需要的扩展信息
	// 系统详细信息
	if metadata.System.Distribution != "" {
		// 如果有发行版信息，使用发行版名称作为OS
		client.OS = metadata.System.Distribution
		client.OSVersion = metadata.System.DistroVersion
	} else {
		// 否则使用原来的逻辑
		client.OSVersion = metadata.System.KernelVersion
	}
	client.KernelVersion = metadata.System.KernelVersion

	// 网络环境信息
	if len(metadata.Network.Interfaces) > 0 {
		// 从第一个活跃接口获取信息
		for _, iface := range metadata.Network.Interfaces {
			if iface.Up && iface.Name != "lo" { // 跳过回环接口
				// 可以根据接口名称判断网络类型
				if iface.Name == "eth0" || iface.Name == "ens33" {
					client.NetworkType = "LAN"
				} else if iface.Name == "wlan0" || iface.Name == "wifi0" {
					client.NetworkType = "WIFI"
				} else {
					client.NetworkType = "OTHER"
				}
				break
			}
		}
	}

	// 环境信息
	client.Domain = ""    // 需要从环境变量或其他方式获取
	client.Workgroup = "" // 需要从环境变量或其他方式获取

	// 地理位置信息 - 从客户端发送的GeoLocation数据中获取
	if metadata.GeoLocation.Country != "" {
		// 构建地理位置字符串：国家-省份-城市
		geoLocation := metadata.GeoLocation.Country
		if metadata.GeoLocation.Province != "" {
			geoLocation += "-" + metadata.GeoLocation.Province
		}
		if metadata.GeoLocation.City != "" {
			geoLocation += "-" + metadata.GeoLocation.City
		}
		client.GeoLocation = geoLocation

		// 保存详细的地理位置信息
		client.Country = metadata.GeoLocation.Country
		client.Province = metadata.GeoLocation.Province
		client.City = metadata.GeoLocation.City
		client.ASN = metadata.GeoLocation.ASN
		client.GeoSource = metadata.GeoLocation.Source
	} else {
		client.GeoLocation = ""
		client.Country = ""
		client.Province = ""
		client.City = ""
		client.ASN = ""
		client.GeoSource = ""
	}

	// ISP信息
	if metadata.GeoLocation.ISP != "" {
		client.ISP = metadata.GeoLocation.ISP
	} else {
		client.ISP = ""
	}

	// 拓扑图位置（初始为0，前端会重新计算）
	client.TopologyX = 0
	client.TopologyY = 0
}

// createHeartbeatConfigForClient 为新客户端创建心跳配置
func (c *ClientManager) createHeartbeatConfigForClient(clientID uint) {
	// 🚀 检查是否已存在该客户端的心跳配置
	var existingConfig sys.HeartbeatConfig
	err := dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_check", func(db *gorm.DB) error {
		return db.Where("client_id = ? AND is_active = ?", clientID, true).First(&existingConfig).Error
	})
	if err == nil {
		// 已存在配置，不需要创建
		global.LOG.Debug("客户端心跳配置已存在，跳过创建", zap.Uint("clientID", clientID))
		return
	}

	// 获取全局配置作为模板
	globalConfig, err := sys.GetGlobalConfig()
	if err != nil {
		global.LOG.Error("获取全局心跳配置失败，无法为客户端创建配置",
			zap.Uint("clientID", clientID),
			zap.Error(err))
		return
	}

	// 基于全局配置创建客户端特定配置
	clientConfig := &sys.HeartbeatConfig{
		ClientID:    clientID,
		Interval:    globalConfig.Interval,    // 继承全局配置
		Timeout:     globalConfig.Timeout,     // 继承全局配置
		MaxRetries:  globalConfig.MaxRetries,  // 继承全局配置
		JitterRange: globalConfig.JitterRange, // 继承全局配置
		IsActive:    &[]bool{true}[0],
		Remark:      fmt.Sprintf("客户端%d的专属心跳配置", clientID),
	}

	// 🚀 直接使用数据库连接池创建配置，避免循环导入
	err = dbpool.ExecuteDBOperationAsyncAndWait("heartbeat_config_create", func(db *gorm.DB) error {
		return db.Create(clientConfig).Error
	})
	if err != nil {
		global.LOG.Error("为客户端创建心跳配置失败",
			zap.Uint("clientID", clientID),
			zap.Error(err))
		return
	}

	global.LOG.Info("成功为客户端创建专属心跳配置",
		zap.Uint("clientID", clientID),
		zap.Uint("configID", clientConfig.ID),
		zap.Int("interval", clientConfig.Interval))
}

func (c *ClientManager) GetClient(clientID uint) (*sys.Client, error) {
	var client sys.Client
	// 🚀 使用数据库连接池进行查询操作
	err := dbpool.ExecuteDBOperationAsyncAndWait("client_get", func(db *gorm.DB) error {
		return db.Where("id = ? AND status = ?", clientID, 1).First(&client).Error
	})

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("客户端不在线或不存在")
		}
		return nil, err
	}
	return &client, nil
}

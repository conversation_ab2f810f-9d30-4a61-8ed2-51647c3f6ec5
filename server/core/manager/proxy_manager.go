package manager

import (
	"errors"
	"fmt"
	"math/rand"
	"net"
	"server/core/manager/dbpool"
	"server/core/manager/proxyserver/forward"
	"server/core/manager/proxyserver/reverse"
	"server/global"
	"server/model/basic"
	"sync"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

const (
	randomAlloc     = "random"
	sequentialAlloc = "sequential"
)

type ProxyManager struct {
	Proxies map[string]*basic.Proxy //proxy的ID为key
	*PortManager
	lock sync.RWMutex

	// 同步相关
	syncTicker   *time.Ticker
	syncStop     chan struct{}
	syncInterval time.Duration
}

type PortRange struct {
	Start uint16
	End   uint16
}

// UserPortManager 用于管理用户连接端口（正向代理和反向代理的用户入口）
type UserPortManager struct {
	PortRange PortRange

	usedPorts         map[uint16]bool // 已使用的用户端口
	lastAllocatedPort uint16          // 上次分配的端口（用于顺序分配）
}

// Socks5PortManager 用于管理反向代理的SOCKS5服务端口（仅反向代理使用）
type Socks5PortManager struct {
	PortRange         PortRange        // SOCKS5端口范围
	usedPorts         map[uint16]bool  // 已使用的SOCKS5端口
	lastAllocatedPort uint16           // 上次分配的端口（用于顺序分配）
}

// PortManager 是总端口管理器，包含用户端口和SOCKS5端口的管理
type PortManager struct {
	UserPortManager   *UserPortManager   // 用户连接端口管理（正向代理和反向代理共用）
	Socks5PortManager *Socks5PortManager // SOCKS5服务端口管理（仅反向代理使用）

	sync.Mutex // 用于并发控制
}

func NewPortManager() *PortManager {
	// 用户端口范围配置（正向代理和反向代理的用户入口）
	userStart := global.CONFIG.Server.ServerStartPort
	userEnd := global.CONFIG.Server.ServerEndPort
	if userStart == 0 {
		userStart = 20000
	}
	if userEnd == 0 {
		userEnd = 65535
	}

	// SOCKS5端口范围配置（仅反向代理使用）
	socks5Start := global.CONFIG.Server.ServerStartPort
	socks5End := global.CONFIG.Server.ServerEndPort
	if socks5Start == 0 {
		socks5Start = userStart
	}
	if socks5End == 0 {
		socks5End = userEnd
	}

	// 初始化 UserPortManager
	userPortManager := &UserPortManager{
		PortRange:         PortRange{Start: userStart, End: userEnd},
		usedPorts:         make(map[uint16]bool),
		lastAllocatedPort: userStart,
	}
	// 🚀 从数据库加载已使用的用户端口（如果数据库已初始化）
	var proxies []basic.Proxy
	dbpool.ExecuteDBOperationAsyncAndWait("proxy_ports_load", func(db *gorm.DB) error {
		if err := db.Model(&basic.Proxy{}).Find(&proxies).Error; err == nil {
			for _, p := range proxies {
				if p.UserPort > 0 {
					userPortManager.usedPorts[p.UserPort] = true
				}
			}
		}
		return nil
	})
	// 初始化 Socks5PortManager（仅反向代理使用）
	socks5PortManager := &Socks5PortManager{
		PortRange:         PortRange{Start: socks5Start, End: socks5End},
		usedPorts:         make(map[uint16]bool),
		lastAllocatedPort: socks5Start,
	}

	return &PortManager{
		UserPortManager:   userPortManager,
		Socks5PortManager: socks5PortManager,
	}
}

func NewProxyManager(manager *PortManager) *ProxyManager {
	pm := &ProxyManager{
		Proxies:      make(map[string]*basic.Proxy),
		PortManager:  manager,
		syncInterval: 30 * time.Second, // 默认30秒同步一次
		syncStop:     make(chan struct{}),
	}

	// 🚀 从数据库恢复代理信息
	var proxies []basic.Proxy
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_manager_init", func(db *gorm.DB) error {
		return db.Model(&basic.Proxy{}).Find(&proxies).Error
	}); err != nil {
		global.LOG.Error("初始化ProxyManager出错", zap.Error(err))
	} else {
		for _, proxy := range proxies {
			// 重置内存标记
			proxy.MemoryDirty = false
			proxy.LastSyncTime = time.Now()
			pm.Proxies[proxy.ProxyID] = &proxy
		}
		global.LOG.Info("从数据库恢复代理信息", zap.Int("count", len(proxies)))
	}

	// 启动定期同步
	pm.startPeriodicSync()

	return pm
}

func (pm *ProxyManager) GetProxy(proxyID string) (*basic.Proxy, bool) {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	proxy, exist := pm.Proxies[proxyID]
	return proxy, exist
}

// AddProxy 添加代理（内存+数据库）
func (pm *ProxyManager) AddProxy(proxy *basic.Proxy) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	// 设置版本和同步信息
	if proxy.Version == 0 {
		proxy.Version = 1
	}
	proxy.MemoryDirty = true
	proxy.LastSyncTime = time.Now()

	// 添加到内存
	pm.Proxies[proxy.ProxyID] = proxy

	// 🚀 异步同步到数据库
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_add", func(db *gorm.DB) error {
		return db.Save(proxy).Error
	}); err != nil {
		// 如果数据库保存失败，从内存中移除
		delete(pm.Proxies, proxy.ProxyID)
		return err
	}

	proxy.MemoryDirty = false
	global.LOG.Info("添加代理成功", zap.String("proxyID", proxy.ProxyID))
	return nil
}

// RemoveProxy 移除代理（内存+数据库）
func (pm *ProxyManager) RemoveProxy(proxyID string) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	proxy, exists := pm.Proxies[proxyID]
	if !exists {
		return fmt.Errorf("代理不存在: %s", proxyID)
	}

	// 🚀 从数据库删除
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_delete", func(db *gorm.DB) error {
		return db.Delete(proxy).Error
	}); err != nil {
		return err
	}

	// 从内存删除
	delete(pm.Proxies, proxyID)

	global.LOG.Info("移除代理成功", zap.String("proxyID", proxyID))
	return nil
}

// UpdateProxy 更新代理（内存+数据库）
func (pm *ProxyManager) UpdateProxy(newProxy *basic.Proxy) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	oldProxy, exists := pm.Proxies[newProxy.ProxyID]
	if !exists {
		return fmt.Errorf("代理不存在: %s", newProxy.ProxyID)
	}

	// 增加版本号
	newProxy.Version = oldProxy.Version + 1
	newProxy.MemoryDirty = true
	newProxy.LastSyncTime = time.Now()

	// 更新内存
	pm.Proxies[newProxy.ProxyID] = newProxy

	// 🚀 同步到数据库
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_update", func(db *gorm.DB) error {
		return db.Save(newProxy).Error
	}); err != nil {
		// 如果数据库保存失败，恢复旧数据
		pm.Proxies[newProxy.ProxyID] = oldProxy
		return err
	}

	newProxy.MemoryDirty = false
	global.LOG.Debug("更新代理成功", zap.String("proxyID", newProxy.ProxyID))
	return nil
}

var (
	GlobalPortManager  *PortManager
	GlobalProxyManager *ProxyManager
	initOnce           sync.Once
)

// InitProxyManagers 初始化代理管理器（在数据库初始化后调用）
func InitProxyManagers() {
	initOnce.Do(func() {
		GlobalPortManager = NewPortManager()
		GlobalProxyManager = NewProxyManager(GlobalPortManager)
	})
}

// GetGlobalPortManager 安全获取全局端口管理器
func GetGlobalPortManager() *PortManager {
	if GlobalPortManager == nil {
		InitProxyManagers()
	}
	return GlobalPortManager
}

// GetGlobalProxyManager 安全获取全局代理管理器
func GetGlobalProxyManager() *ProxyManager {
	if GlobalProxyManager == nil {
		InitProxyManagers()
	}
	return GlobalProxyManager
}

// ===== 同步相关方法 =====

// startPeriodicSync 启动定期同步
func (pm *ProxyManager) startPeriodicSync() {
	pm.syncTicker = time.NewTicker(pm.syncInterval)
	go func() {
		for {
			select {
			case <-pm.syncTicker.C:
				pm.syncToDatabase()
			case <-pm.syncStop:
				pm.syncTicker.Stop()
				return
			}
		}
	}()
}

// StopPeriodicSync 停止定期同步
func (pm *ProxyManager) StopPeriodicSync() {
	if pm.syncStop != nil {
		close(pm.syncStop)
	}
}

// syncToDatabase 同步内存数据到数据库
func (pm *ProxyManager) syncToDatabase() {
	pm.lock.RLock()
	var dirtyProxies []*basic.Proxy
	for _, proxy := range pm.Proxies {
		if proxy.MemoryDirty {
			dirtyProxies = append(dirtyProxies, proxy)
		}
	}
	pm.lock.RUnlock()

	if len(dirtyProxies) == 0 {
		return
	}

	// 🚀 批量更新数据库
	for _, proxy := range dirtyProxies {
		if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_sync", func(db *gorm.DB) error {
			return db.Save(proxy).Error
		}); err != nil {
			global.LOG.Error("同步代理到数据库失败",
				zap.String("proxyID", proxy.ProxyID),
				zap.Error(err))
		} else {
			pm.lock.Lock()
			proxy.MemoryDirty = false
			proxy.LastSyncTime = time.Now()
			pm.lock.Unlock()
		}
	}

	global.LOG.Debug("同步代理到数据库完成", zap.Int("count", len(dirtyProxies)))
}

// forceSyncToDatabase 强制同步所有数据到数据库
func (pm *ProxyManager) forceSyncToDatabase() {
	pm.lock.RLock()
	proxies := make([]*basic.Proxy, 0, len(pm.Proxies))
	for _, proxy := range pm.Proxies {
		proxies = append(proxies, proxy)
	}
	pm.lock.RUnlock()

	for _, proxy := range proxies {
		// 🚀 使用数据库连接池异步同步代理到数据库
		if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_force_sync", func(db *gorm.DB) error {
			return db.Save(proxy).Error
		}); err != nil {
			global.LOG.Error("强制同步代理到数据库失败",
				zap.String("proxyID", proxy.ProxyID),
				zap.Error(err))
		} else {
			pm.lock.Lock()
			proxy.MemoryDirty = false
			proxy.LastSyncTime = time.Now()
			pm.lock.Unlock()
		}
	}

	global.LOG.Info("强制同步所有代理到数据库完成", zap.Int("count", len(proxies)))
}

// AllocUserPort 分配用户连接端口（正向代理和反向代理共用）
func (pm *PortManager) AllocUserPort(mode string) (uint16, error) {
	pm.Lock()
	defer pm.Unlock()

	return pm.UserPortManager.AllocPort(mode)
}

// AllocServerPort 保持兼容性的别名
func (pm *PortManager) AllocServerPort(mode string) (uint16, error) {
	return pm.AllocUserPort(mode)
}

// StartProxy 统一的代理启动接口
func (pm *ProxyManager) StartProxy(proxy *basic.Proxy) error {
	switch proxy.Type {
	case "forward": // 正向代理
		return pm.StartForwardProxy(proxy)
	case "reverse": // 反向代理
		return pm.StartReverseProxy(proxy)
	default:
		return fmt.Errorf("unsupported proxy type: %s", proxy.Type)
	}
}

// RestartProxy 重启代理
func (pm *ProxyManager) RestartProxy(proxy *basic.Proxy) error {
	if err := pm.StopProxy(proxy); err != nil {
		global.LOG.Warn("停止代理时出错", zap.Error(err))
	}
	return pm.StartProxy(proxy)
}

// GetProxyStats 获取代理统计信息
func (pm *ProxyManager) GetProxyStats() map[string]interface{} {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	stats := map[string]interface{}{
		"total":   len(pm.Proxies),
		"running": 0,
		"stopped": 0,
		"error":   0,
		"forward": 0,
		"reverse": 0,
	}

	for _, proxy := range pm.Proxies {
		switch proxy.Status {
		case 0:
			stats["stopped"] = stats["stopped"].(int) + 1
		case 1:
			stats["running"] = stats["running"].(int) + 1
		case 2:
			stats["error"] = stats["error"].(int) + 1
		}

		switch proxy.Type {
		case "forward":
			stats["forward"] = stats["forward"].(int) + 1
		case "reverse":
			stats["reverse"] = stats["reverse"].(int) + 1
		}
	}

	return stats
}

func (pm *ProxyManager) StartForwardProxy(proxy *basic.Proxy) error {
	// 正向代理不需要在这里分配userPort，应该已经分配好了
	// 直接启动中继服务
	if err := forward.GlobalForwardManager.StartServer(proxy); err != nil {
		return err
	}

	return nil
}

func (pm *ProxyManager) StopForwardProxy(proxy *basic.Proxy) error {
	if err := forward.GlobalForwardManager.StopServer(proxy); err != nil {
		return err
	}
	return nil
}

func (pm *ProxyManager) StartReverseProxy(proxy *basic.Proxy) error {
	// 使用新的反向代理实现
	if err := reverse.GlobalReverseManager.StartServer(proxy); err != nil {
		return err
	}
	return nil
}

func (pm *ProxyManager) StopReverseProxy(proxy *basic.Proxy) error {
	// 使用新的反向代理实现
	if err := reverse.GlobalReverseManager.StopServer(proxy); err != nil {
		return err
	}
	return nil
}

func (pm *ProxyManager) StopProxy(proxy *basic.Proxy) error {
	switch proxy.Type {
	case "forward": // 正向代理
		// 停止正向代理服务
		if err := pm.StopForwardProxy(proxy); err != nil {
			global.LOG.Error("停止正向代理失败", zap.Error(err))
			return err
		}
		// 回收服务器端口
		if proxy.UserPort > 0 {
			pm.PortManager.ReleaseUserPort(proxy.UserPort)
		}
		// 正向代理不需要回收ClientPort（客户端自己管理）

	case "reverse": // 反向代理
		// 停止反向代理服务
		if err := pm.StopReverseProxy(proxy); err != nil {
			global.LOG.Error("停止反向代理失败", zap.Error(err))
			return err
		}
		// 回收服务器端口（UserPort）
		if proxy.UserPort > 0 {
			pm.PortManager.ReleaseUserPort(proxy.UserPort)
		}
		// 回收SOCKS5端口（反向代理的ClientPort）
		if proxy.ClientPort > 0 {
			pm.PortManager.ReleaseSocks5Port(proxy.ClientPort)
		}

	default:
		return fmt.Errorf("unsupported proxy type: %s", proxy.Type)
	}

	// 更新代理状态（通过UpdateProxy自动同步）
	proxy.Status = 0
	if err := pm.UpdateProxy(proxy); err != nil {
		global.LOG.Error("更新代理状态失败", zap.Error(err))
		return err
	}

	return nil
}

// GetProxyByUserPort 根据User端口获取代理
func (pm *ProxyManager) GetProxyByUserPort(port uint16) *basic.Proxy {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	for _, proxy := range pm.Proxies {
		if proxy.UserPort == port {
			return proxy
		}
	}
	return nil
}

// GetProxiesByClient 获取指定客户端的所有代理
func (pm *ProxyManager) GetProxiesByClient(clientID uint) []*basic.Proxy {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	var proxies []*basic.Proxy
	for _, proxy := range pm.Proxies {
		if proxy.ClientID == clientID {
			proxies = append(proxies, proxy)
		}
	}
	return proxies
}

// GetProxiesByStatus 获取指定状态的代理
func (pm *ProxyManager) GetProxiesByStatus(status int) []*basic.Proxy {
	pm.lock.RLock()
	defer pm.lock.RUnlock()
	var proxies []*basic.Proxy
	for _, proxy := range pm.Proxies {
		if proxy.Status == status {
			proxies = append(proxies, proxy)
		}
	}
	return proxies
}

// GetAllProxies 获取所有代理
func (pm *ProxyManager) GetAllProxies() []*basic.Proxy {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	var proxies []*basic.Proxy
	for _, proxy := range pm.Proxies {
		proxies = append(proxies, proxy)
	}
	return proxies
}

// ProxyExists 检查代理是否存在
func (pm *ProxyManager) ProxyExists(proxyID string) bool {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	_, exists := pm.Proxies[proxyID]
	return exists
}

// UpdateProxyStatus 更新代理状态
func (pm *ProxyManager) UpdateProxyStatus(proxyID string, status int) error {
	pm.lock.Lock()
	defer pm.lock.Unlock()
	if proxy, exists := pm.Proxies[proxyID]; exists {
		proxy.Status = status
		proxy.Version++
		proxy.MemoryDirty = true
		proxy.LastSyncTime = time.Now()
		// 🚀 使用数据库连接池更新状态
		return dbpool.ExecuteDBOperationAsyncAndWait("proxy_status_update", func(db *gorm.DB) error {
			return db.Model(proxy).Update("status", status).Error
		})
	}
	return fmt.Errorf("proxy %s not found", proxyID)
}

// CleanupStoppedProxies 清理已停止的代理
func (pm *ProxyManager) CleanupStoppedProxies() error {
	pm.lock.Lock()
	defer pm.lock.Unlock()

	var toDelete []string
	for id, proxy := range pm.Proxies {
		if proxy.Status == 0 { // 已停止
			toDelete = append(toDelete, id)
		}
	}

	for _, id := range toDelete {
		delete(pm.Proxies, id)
		// 🚀 使用数据库连接池异步删除代理记录
		if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_cleanup_delete", func(db *gorm.DB) error {
			return db.Delete(&basic.Proxy{}, id).Error
		}); err != nil {
			global.LOG.Error("删除代理记录失败", zap.String("proxyID", id), zap.Error(err))
		}
	}

	return nil
}

// SyncWithDatabase 与数据库同步
func (pm *ProxyManager) SyncWithDatabase() error {
	var proxies []basic.Proxy
	// 🚀 使用数据库连接池同步代理数据
	if err := dbpool.ExecuteDBOperationAsyncAndWait("proxy_sync_all", func(db *gorm.DB) error {
		return db.Model(&basic.Proxy{}).Find(&proxies).Error
	}); err != nil {
		pm.forceSyncToDatabase()
		return err
	}

	pm.lock.Lock()
	defer pm.lock.Unlock()

	// 清空内存中的代理
	pm.Proxies = make(map[string]*basic.Proxy)

	// 重新加载
	for _, proxy := range proxies {
		pm.Proxies[proxy.ProxyID] = &proxy
	}

	return nil
}

// ValidateProxy 验证代理配置
func (pm *ProxyManager) ValidateProxy(proxy *basic.Proxy, checkPortAvailability ...bool) error {
	if proxy.ClientID == 0 {
		return errors.New("client ID cannot be zero")
	}

	if proxy.Type != "forward" && proxy.Type != "reverse" {
		return fmt.Errorf("invalid proxy type: %s", proxy.Type)
	}

	// 默认检查端口可用性，但可以通过参数禁用（如停止代理时）
	shouldCheckPort := true
	if len(checkPortAvailability) > 0 {
		shouldCheckPort = checkPortAvailability[0]
	}

	if shouldCheckPort && proxy.UserPort != 0 {
		if ok, _ := CheckPortAvailable(proxy.UserPort); !ok {
			return fmt.Errorf("port %d is not available", proxy.UserPort)
		}
	}
	return nil
}

// AllocPort 分配用户端口
func (upm *UserPortManager) AllocPort(mode string) (uint16, error) {
	start := upm.PortRange.Start
	end := upm.PortRange.End

	if start == 0 || end == 0 || start > end {
		return 0, fmt.Errorf("invalid user port range: %d - %d", start, end)
	}

	var port uint16
	var found bool

	switch mode {
	case "random":
		for i := 0; i <= int(end-start); i++ {
			port = uint16(rand.Intn(int(end-start)+1)) + start
			if !upm.usedPorts[port] {
				if ok, _ := CheckPortAvailable(port); ok {
					found = true
					break
				}
			}
		}
	case "sequential":
		current := upm.lastAllocatedPort
		if current < start || current > end {
			current = start
		}
		for i := 0; i <= int(end-start); i++ {
			port = ((current-start)+1+uint16(i))%(end-start+1) + start
			if !upm.usedPorts[port] {
				if ok, _ := CheckPortAvailable(port); ok {
					upm.lastAllocatedPort = port
					found = true
					break
				}
			}
		}
	default:
		return 0, fmt.Errorf("unknown allocation mode: %s", mode)
	}

	if found {
		upm.usedPorts[port] = true
		return port, nil
	}

	return 0, fmt.Errorf("no available user port in range %d - %d", start, end)
}

// AllocPort 分配SOCKS5端口
func (spm *Socks5PortManager) AllocPort(mode string) (uint16, error) {
	start := spm.PortRange.Start
	end := spm.PortRange.End

	if start == 0 || end == 0 || start > end {
		return 0, fmt.Errorf("invalid SOCKS5 port range: %d - %d", start, end)
	}

	var port uint16
	var found bool

	switch mode {
	case "random":
		for i := 0; i <= int(end-start); i++ {
			port = uint16(rand.Intn(int(end-start)+1)) + start
			if !spm.usedPorts[port] {
				if ok, _ := CheckPortAvailable(port); ok {
					found = true
					break
				}
			}
		}
	case "sequential":
		current := spm.lastAllocatedPort
		if current < start || current > end {
			current = start
		}
		for i := 0; i <= int(end-start); i++ {
			port = ((current-start)+1+uint16(i))%(end-start+1) + start
			if !spm.usedPorts[port] {
				if ok, _ := CheckPortAvailable(port); ok {
					spm.lastAllocatedPort = port
					found = true
					break
				}
			}
		}
	default:
		return 0, fmt.Errorf("unknown allocation mode: %s", mode)
	}

	if found {
		spm.usedPorts[port] = true
		return port, nil
	}

	return 0, fmt.Errorf("no available SOCKS5 port in range %d - %d", start, end)
}

// CheckPortAvailable 检查指定端口是否可用
func CheckPortAvailable(port uint16) (bool, string) {
	tcpAddr := fmt.Sprintf(":%d", port)
	tcpListener, err := net.Listen("tcp", tcpAddr)
	if err != nil {
		return false, fmt.Sprintf("TCP端口被占用: %v", err)
	}
	tcpListener.Close()

	udpAddr := fmt.Sprintf(":%d", port)
	udpConn, err := net.ListenPacket("udp", udpAddr)
	if err != nil {
		return false, fmt.Sprintf("UDP端口被占用: %v", err)
	}
	udpConn.Close()

	return true, "端口可用"
}

// GetUserPortUsage 获取用户端口使用情况
func (pm *PortManager) GetUserPortUsage() map[string]interface{} {
	pm.Lock()
	defer pm.Unlock()

	upm := pm.UserPortManager
	total := int(upm.PortRange.End - upm.PortRange.Start + 1)
	used := len(upm.usedPorts)

	return map[string]interface{}{
		"total":     total,
		"used":      used,
		"available": total - used,
		"usage":     float64(used) / float64(total) * 100,
		"range":     fmt.Sprintf("%d-%d", upm.PortRange.Start, upm.PortRange.End),
	}
}

// GetServerPortUsage 保持兼容性的别名
func (pm *PortManager) GetServerPortUsage() map[string]interface{} {
	return pm.GetUserPortUsage()
}

// GetSocks5PortUsage 获取SOCKS5端口使用情况
func (pm *PortManager) GetSocks5PortUsage() map[string]interface{} {
	pm.Lock()
	defer pm.Unlock()

	spm := pm.Socks5PortManager
	total := int(spm.PortRange.End - spm.PortRange.Start + 1)
	used := len(spm.usedPorts)

	return map[string]interface{}{
		"total":     total,
		"used":      used,
		"available": total - used,
		"usage":     float64(used) / float64(total) * 100,
	}
}



// IsUserPortUsed 检查用户端口是否被使用
func (pm *PortManager) IsUserPortUsed(port uint16) bool {
	pm.Lock()
	defer pm.Unlock()
	return pm.UserPortManager.usedPorts[port]
}


// IsSocks5PortUsed 检查SOCKS5端口是否被使用
func (pm *PortManager) IsSocks5PortUsed(port uint16) bool {
	pm.Lock()
	defer pm.Unlock()
	return pm.Socks5PortManager.usedPorts[port]
}


// AllocSocks5Port 分配反向代理的SOCKS5服务端口
func (pm *PortManager) AllocSocks5Port(mode string) (uint16, error) {
	pm.Lock()
	defer pm.Unlock()

	return pm.Socks5PortManager.AllocPort(mode)
}



// ReleaseSocks5Port 释放SOCKS5端口
func (pm *PortManager) ReleaseSocks5Port(port uint16) {
	pm.Lock()
	defer pm.Unlock()

	spm := pm.Socks5PortManager
	if spm.lastAllocatedPort == port {
		spm.lastAllocatedPort -= 1
	}
	delete(spm.usedPorts, port)
}

// MarkSocks5PortUsed 标记SOCKS5端口为已使用
func (pm *PortManager) MarkSocks5PortUsed(port uint16) {
	pm.Lock()
	defer pm.Unlock()

	pm.Socks5PortManager.usedPorts[port] = true
}

// ReleaseUserPort 释放用户端口
func (pm *PortManager) ReleaseUserPort(port uint16) {
	pm.Lock()
	defer pm.Unlock()

	upm := pm.UserPortManager
	if upm.lastAllocatedPort == port {
		upm.lastAllocatedPort -= 1
	}
	delete(upm.usedPorts, port)
}

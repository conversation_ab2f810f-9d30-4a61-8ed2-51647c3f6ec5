// forward/forward_manager.go
package forward

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net"
	"server/core/manager/dbpool"
	"server/global"
	"server/model/basic"
	"server/model/sys"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

/*
正向代理的实现

User (外网)
 |
 v
(userPort)
Server (作为socks5客户端) <===> Client (内网的socks5服务)

userPort面向用户: 可以由用户自定义，也可以自动分配
Client运行socks5服务: 由Client启动并告知Server端口

支持多层内网穿透：
第一层：User -> Server:1080 -> Client1:10000
第二层：User -> Server:1080 -> Client1:20000 -> Client2:10000
第三层：User -> Server:1080 -> Client1:20000 -> Client2:30000 -> Client3:10000

端口复用：用户可以一直使用同一个Server端口（如1080）穿透多层内网
*/

// ===== 模块化组件 =====

// ForwardProxyConfig 正向代理配置
type ForwardProxyConfig struct {
	*basic.Proxy
	allowedIPs []string
	blockedIPs []string
}

// NewForwardProxyConfig 创建正向代理配置
func NewForwardProxyConfig(proxy *basic.Proxy) *ForwardProxyConfig {
	config := &ForwardProxyConfig{Proxy: proxy}

	// 解析IP列表
	if proxy.AllowedIPs != "" {
		config.allowedIPs = parseIPList(proxy.AllowedIPs)
	}
	if proxy.BlockedIPs != "" {
		config.blockedIPs = parseIPList(proxy.BlockedIPs)
	}

	return config
}

// parseIPList 解析逗号分隔的IP列表
func parseIPList(ipStr string) []string {
	if ipStr == "" {
		return nil
	}
	ips := strings.Split(ipStr, ",")
	result := make([]string, 0, len(ips))
	for _, ip := range ips {
		ip = strings.TrimSpace(ip)
		if ip != "" {
			result = append(result, ip)
		}
	}
	return result
}

// ForwardAccessController 正向代理访问控制器
type ForwardAccessController struct {
	config *ForwardProxyConfig
}

// NewForwardAccessController 创建访问控制器
func NewForwardAccessController(config *ForwardProxyConfig) *ForwardAccessController {
	return &ForwardAccessController{config: config}
}

// IsAllowed 检查IP是否被允许访问
func (ac *ForwardAccessController) IsAllowed(clientIP string) bool {
	// 首先检查黑名单
	for _, blockedIP := range ac.config.blockedIPs {
		if matchIP(clientIP, blockedIP) {
			return false
		}
	}

	// 如果没有白名单，则允许（除非在黑名单中）
	if len(ac.config.allowedIPs) == 0 {
		return true
	}

	// 检查白名单
	for _, allowedIP := range ac.config.allowedIPs {
		if matchIP(clientIP, allowedIP) {
			return true
		}
	}

	return false
}

// matchIP 简单的IP匹配（支持通配符*）
func matchIP(clientIP, pattern string) bool {
	if pattern == "*" {
		return true
	}
	if pattern == clientIP {
		return true
	}
	// 简单的前缀匹配，如 192.168.1.*
	if strings.HasSuffix(pattern, "*") {
		prefix := strings.TrimSuffix(pattern, "*")
		return strings.HasPrefix(clientIP, prefix)
	}
	return false
}

// ForwardProxyStats 正向代理统计信息
type ForwardProxyStats struct {
	totalConnections  int64
	activeConnections int64
	bytesTransferred  int64
	bytesReceived     int64
	lastActivity      time.Time
	mutex             sync.RWMutex

	// 定期更新相关
	updateTicker *time.Ticker
	updateStop   chan struct{}
	proxy        *basic.Proxy
}

// NewForwardProxyStats 创建统计对象
func NewForwardProxyStats(proxy *basic.Proxy) *ForwardProxyStats {
	stats := &ForwardProxyStats{
		lastActivity: time.Now(),
		updateStop:   make(chan struct{}),
		proxy:        proxy,
	}

	// 启动定期更新
	stats.startPeriodicUpdate()

	return stats
}

// AddConnection 增加连接计数
func (ps *ForwardProxyStats) AddConnection() {
	atomic.AddInt64(&ps.totalConnections, 1)
	atomic.AddInt64(&ps.activeConnections, 1)
	ps.updateLastActivity()
}

// RemoveConnection 减少连接计数
func (ps *ForwardProxyStats) RemoveConnection() {
	atomic.AddInt64(&ps.activeConnections, -1)
}

// AddBytes 增加传输字节数
func (ps *ForwardProxyStats) AddBytes(sent, received int64) {
	atomic.AddInt64(&ps.bytesTransferred, sent)
	atomic.AddInt64(&ps.bytesReceived, received)
	ps.updateLastActivity()
}

// updateLastActivity 更新最后活动时间
func (ps *ForwardProxyStats) updateLastActivity() {
	ps.mutex.Lock()
	ps.lastActivity = time.Now()
	ps.mutex.Unlock()
}

// GetStats 获取统计信息
func (ps *ForwardProxyStats) GetStats() (int64, int64, int64, int64, time.Time) {
	ps.mutex.RLock()
	defer ps.mutex.RUnlock()
	return atomic.LoadInt64(&ps.totalConnections),
		atomic.LoadInt64(&ps.activeConnections),
		atomic.LoadInt64(&ps.bytesTransferred),
		atomic.LoadInt64(&ps.bytesReceived),
		ps.lastActivity
}

// startPeriodicUpdate 启动定期更新统计信息到数据库
func (ps *ForwardProxyStats) startPeriodicUpdate() {
	ps.updateTicker = time.NewTicker(10 * time.Second) // 每10秒更新一次
	go func() {
		for {
			select {
			case <-ps.updateTicker.C:
				ps.updateToDatabase()
			case <-ps.updateStop:
				ps.updateTicker.Stop()
				return
			}
		}
	}()
}

// stopPeriodicUpdate 停止定期更新
func (ps *ForwardProxyStats) stopPeriodicUpdate() {
	if ps.updateStop != nil {
		close(ps.updateStop)
	}
}

// updateToDatabase 更新统计信息到数据库
func (ps *ForwardProxyStats) updateToDatabase() {
	if ps.proxy == nil {
		return
	}

	totalConn, activeConn, bytesSent, bytesReceived, lastActivity := ps.GetStats()

	// 直接更新数据库中的统计字段
	updates := map[string]interface{}{
		"total_connections":  totalConn,
		"active_connections": int(activeConn),
		"bytes_transferred":  bytesSent,
		"bytes_received":     bytesReceived,
		"last_activity":      lastActivity,
		"version":            ps.proxy.Version + 1,
	}

	// 🚀 使用数据库连接池异步更新正向代理统计信息
	if err := dbpool.ExecuteDBOperationAsyncAndWait("forward_proxy_stats_update", func(db *gorm.DB) error {
		return db.Model(ps.proxy).Updates(updates).Error
	}); err != nil {
		global.LOG.Error("更新正向代理统计信息失败",
			zap.String("proxyID", ps.proxy.ProxyID),
			zap.Error(err))
	} else {
		// 更新内存中的版本号
		ps.proxy.Version++
		ps.proxy.TotalConnections = totalConn
		ps.proxy.ActiveConnections = int(activeConn)
		ps.proxy.BytesTransferred = bytesSent
		ps.proxy.BytesReceived = bytesReceived
		ps.proxy.LastActivity = lastActivity
	}
}

// ForwardConnectionManager 正向代理连接管理器
type ForwardConnectionManager struct {
	connections map[string]net.Conn
	mutex       sync.RWMutex
}

// NewForwardConnectionManager 创建连接管理器
func NewForwardConnectionManager() *ForwardConnectionManager {
	return &ForwardConnectionManager{
		connections: make(map[string]net.Conn),
	}
}

// AddConnection 添加连接
func (cm *ForwardConnectionManager) AddConnection(id string, conn net.Conn) {
	cm.mutex.Lock()
	cm.connections[id] = conn
	cm.mutex.Unlock()
}

// RemoveConnection 移除连接
func (cm *ForwardConnectionManager) RemoveConnection(id string) {
	cm.mutex.Lock()
	if conn, exists := cm.connections[id]; exists {
		conn.Close()
		delete(cm.connections, id)
	}
	cm.mutex.Unlock()
}

// CloseAll 关闭所有连接
func (cm *ForwardConnectionManager) CloseAll() {
	cm.mutex.Lock()
	for id, conn := range cm.connections {
		conn.Close()
		delete(cm.connections, id)
	}
	cm.mutex.Unlock()
}

// ===== 主要结构体 =====

// ForwardProxyServer 正向代理服务器实例
type ForwardProxyServer struct {
	// 基本配置
	userPort     uint16
	clientPort   uint16
	clientAddr   string
	userListener net.Listener

	// 模块化组件
	config            *ForwardProxyConfig
	accessController  *ForwardAccessController
	stats             *ForwardProxyStats
	connectionManager *ForwardConnectionManager

	// 控制
	ctx     context.Context
	cancel  context.CancelFunc
	running bool
	lock    sync.RWMutex
}

// ForwardManager 正向代理管理器
type ForwardManager struct {
	servers map[*basic.Proxy]*ForwardProxyServer
	lock    sync.Mutex

	// 端口复用管理
	portReuse map[uint16][]*ForwardProxyServer // 端口 -> 服务器列表
	portLock  sync.RWMutex
}

var GlobalForwardManager = &ForwardManager{
	servers:   make(map[*basic.Proxy]*ForwardProxyServer),
	portReuse: make(map[uint16][]*ForwardProxyServer),
}

// StartServer 启动正向代理服务
func (fm *ForwardManager) StartServer(proxy *basic.Proxy) error {
	if global.LOG != nil {
		global.LOG.Info("🚀 [FORWARD] 开始启动正向代理服务",
			zap.String("proxyID", proxy.ProxyID),
			zap.String("name", proxy.Name),
			zap.Uint("clientID", proxy.ClientID),
			zap.Uint16("userPort", proxy.UserPort),
			zap.Uint16("clientPort", proxy.ClientPort))
	}

	fm.lock.Lock()
	defer fm.lock.Unlock()

	if _, exists := fm.servers[proxy]; exists {
		if global.LOG != nil {
			global.LOG.Warn("⚠️ [FORWARD] 正向代理服务器已存在", zap.String("proxyID", proxy.ProxyID))
		}
		return errors.New("正向代理服务器已存在")
	}

	// 🚀 获取客户端信息
	if global.LOG != nil {
		global.LOG.Info("📋 [FORWARD] 获取客户端信息", zap.Uint("clientID", proxy.ClientID))
	}

	var client sys.Client
	if err := dbpool.ExecuteDBOperationAsyncAndWait("forward_client_info", func(db *gorm.DB) error {
		return db.Model(&sys.Client{}).Where("id = ?", proxy.ClientID).First(&client).Error
	}); err != nil {
		if global.LOG != nil {
			global.LOG.Error("🔴 [FORWARD] 获取客户端信息失败",
				zap.Uint("clientID", proxy.ClientID),
				zap.Error(err))
		}
		return fmt.Errorf("客户端不存在: %v", err)
	}

	if global.LOG != nil {
		global.LOG.Info("✅ [FORWARD] 客户端信息获取成功",
			zap.Uint("clientID", proxy.ClientID),
			zap.String("hostname", client.Hostname),
			zap.String("remoteAddr", client.RemoteAddr),
			zap.String("os", client.OS),
			zap.Int("status", client.Status))
	}

	ctx, cancel := context.WithCancel(context.Background())

	// 创建模块化组件
	if global.LOG != nil {
		global.LOG.Info("🔧 [FORWARD] 创建正向代理组件")
	}

	config := NewForwardProxyConfig(proxy)
	accessController := NewForwardAccessController(config)
	stats := NewForwardProxyStats(proxy)
	connectionManager := NewForwardConnectionManager()

	// 从客户端地址中提取IP地址
	clientIP, _, err := net.SplitHostPort(client.RemoteAddr)
	if err != nil {
		if global.LOG != nil {
			global.LOG.Error("🔴 [FORWARD] 解析客户端地址失败",
				zap.String("remoteAddr", client.RemoteAddr),
				zap.Error(err))
		}
		cancel()
		return fmt.Errorf("解析客户端地址失败: %v", err)
	}

	server := &ForwardProxyServer{
		userPort:          proxy.UserPort,
		clientPort:        proxy.ClientPort,
		clientAddr:        clientIP, // 只保存IP地址，不包含端口
		config:            config,
		accessController:  accessController,
		stats:             stats,
		connectionManager: connectionManager,
		ctx:               ctx,
		cancel:            cancel,
		running:           false,
	}

	if global.LOG != nil {
		global.LOG.Info("🚀 [FORWARD] 启动正向代理服务器",
			zap.Uint16("userPort", proxy.UserPort),
			zap.Uint16("clientPort", proxy.ClientPort),
			zap.String("clientAddr", client.RemoteAddr))
	}

	if err := server.Start(proxy); err != nil {
		if global.LOG != nil {
			global.LOG.Error("🔴 [FORWARD] 正向代理服务器启动失败",
				zap.String("proxyID", proxy.ProxyID),
				zap.Error(err))
		}
		cancel()
		return err
	}

	// 启动成功后添加到map中
	fm.servers[proxy] = server

	if global.LOG != nil {
		global.LOG.Info("✅ [FORWARD] 正向代理服务器启动成功",
			zap.String("proxyID", proxy.ProxyID),
			zap.String("name", proxy.Name))
	}

	return nil
}

// Start 启动正向代理服务
func (s *ForwardProxyServer) Start(proxy *basic.Proxy) error {
	if global.LOG != nil {
		global.LOG.Info("🚀 [FORWARD-SERVER] 开始启动正向代理服务",
			zap.String("proxyID", proxy.ProxyID),
			zap.Uint16("userPort", s.userPort),
			zap.Uint16("clientPort", s.clientPort),
			zap.String("clientAddr", s.clientAddr))
	}

	s.lock.Lock()
	if s.running {
		s.lock.Unlock()
		if global.LOG != nil {
			global.LOG.Warn("⚠️ [FORWARD-SERVER] 服务已在运行", zap.String("proxyID", proxy.ProxyID))
		}
		return errors.New("服务已在运行")
	}
	s.running = true
	s.lock.Unlock()

	// 启动用户监听器
	userAddr := fmt.Sprintf(":%d", s.userPort)
	if global.LOG != nil {
		global.LOG.Info("🌐 [FORWARD-SERVER] 启动用户监听器",
			zap.String("userAddr", userAddr),
			zap.Uint16("userPort", s.userPort))
	}

	userListener, err := net.Listen("tcp", userAddr)
	if err != nil {
		s.lock.Lock()
		s.running = false
		s.lock.Unlock()
		if global.LOG != nil {
			global.LOG.Error("🔴 [FORWARD-SERVER] 启动用户监听器失败",
				zap.String("userAddr", userAddr),
				zap.Error(err))
		}
		return fmt.Errorf("启动用户监听器失败: %v", err)
	}
	s.userListener = userListener

	if global.LOG != nil {
		global.LOG.Info("✅ [FORWARD-SERVER] 用户监听器启动成功",
			zap.String("userAddr", userAddr))
	}

	// 更新代理状态和启动时间
	s.config.Status = 1 // 运行中
	s.config.StartedAt = time.Now()
	s.config.ErrorCount = 0
	s.config.LastError = ""

	// 注意：这里不需要手动保存，因为统计信息会通过定期更新自动保存

	if global.LOG != nil {
		global.LOG.Info("✅ [FORWARD-SERVER] 正向代理启动成功",
			zap.Uint16("userPort", s.userPort),
			zap.Uint16("clientPort", s.clientPort),
			zap.String("clientAddr", s.clientAddr),
			zap.Bool("authRequired", s.config.AuthRequired),
			zap.Int("allowedIPs", len(s.config.allowedIPs)),
			zap.Int("blockedIPs", len(s.config.blockedIPs)))
	}

	// 启动用户连接处理goroutine
	if global.LOG != nil {
		global.LOG.Info("🔄 [FORWARD-SERVER] 启动用户连接处理协程")
	}
	go s.handleUserConnections()

	return nil
}

// handleUserConnections 处理用户连接
func (s *ForwardProxyServer) handleUserConnections() {
	defer s.userListener.Close()

	for {
		select {
		case <-s.ctx.Done():
			return
		default:
			conn, err := s.userListener.Accept()
			if err != nil {
				select {
				case <-s.ctx.Done():
					return
				default:
					if global.LOG != nil {
						global.LOG.Error("接受连接失败", zap.Error(err))
					}
					continue
				}
			}
			go s.handleUserConnection(conn)
		}
	}
}

// handleUserConnection 处理单个用户连接，转发到Client的socks5服务
func (s *ForwardProxyServer) handleUserConnection(userConn net.Conn) {
	defer userConn.Close()

	// 获取客户端IP
	clientIP := ""
	if addr, ok := userConn.RemoteAddr().(*net.TCPAddr); ok {
		clientIP = addr.IP.String()
	}

	// 访问控制检查
	if !s.accessController.IsAllowed(clientIP) {
		global.LOG.Warn("拒绝连接：IP不在允许列表中", zap.String("clientIP", clientIP))
		return
	}

	// 统计连接
	s.stats.AddConnection()
	defer s.stats.RemoveConnection()

	// 生成连接ID并添加到连接管理器
	connID := fmt.Sprintf("%s-%d", clientIP, time.Now().UnixNano())
	s.connectionManager.AddConnection(connID, userConn)
	defer s.connectionManager.RemoveConnection(connID)

	global.LOG.Info("正向代理用户连接已建立",
		zap.String("clientIP", clientIP),
		zap.String("connID", connID))

	// 连接到Client的socks5服务
	// 注意：这里需要使用客户端实际启动的SOCKS5端口，而不是服务端分配的clientPort
	// 客户端的SOCKS5端口在代理启动成功后会更新到数据库中
	clientAddr := fmt.Sprintf("%s:%d", s.clientAddr, s.clientPort)

	if global.LOG != nil {
		global.LOG.Info("🔗 [FORWARD] 尝试连接客户端SOCKS5服务",
			zap.String("clientIP", s.clientAddr),
			zap.Uint16("clientPort", s.clientPort),
			zap.String("fullAddr", clientAddr))
	}

	clientConn, err := net.DialTimeout("tcp", clientAddr, 10*time.Second)
	if err != nil {
		if global.LOG != nil {
			global.LOG.Error("连接到客户端socks5服务失败",
				zap.String("clientAddr", clientAddr),
				zap.Error(err),
				zap.String("诊断建议", "请检查: 1)客户端SOCKS5服务是否正常启动 2)防火墙是否阻止端口 3)网络连通性"))
		}
		return
	}
	defer clientConn.Close()



	// 使用带统计功能的数据转发
	done := make(chan struct{}, 2)

	// 用户 -> Client
	go func() {
		defer func() { done <- struct{}{} }()
		sent, err := io.Copy(clientConn, userConn)
		s.stats.AddBytes(sent, 0)
		if err != nil && global.LOG != nil {
			global.LOG.Debug("用户到客户端数据转发结束", zap.Error(err))
		}
	}()

	// Client -> 用户
	go func() {
		defer func() { done <- struct{}{} }()
		received, err := io.Copy(userConn, clientConn)
		s.stats.AddBytes(0, received)
		if err != nil && global.LOG != nil {
			global.LOG.Debug("客户端到用户数据转发结束", zap.Error(err))
		}
	}()

	// 等待任一方向的转发结束
	<-done
}

// StartForward 启动正向代理（兼容旧接口）
func (fm *ForwardManager) StartForward(serverPort uint16, proxy *basic.Proxy, clientPort uint16) error {
	// 更新proxy的端口信息
	proxy.UserPort = serverPort
	proxy.ClientPort = clientPort
	return fm.StartServer(proxy)
}

// StopServer 停止正向代理服务
func (fm *ForwardManager) StopServer(proxy *basic.Proxy) error {
	fm.lock.Lock()
	defer fm.lock.Unlock()

	server, exists := fm.servers[proxy]
	if !exists {
		return nil // 服务不存在，认为已停止
	}

	// 调用 Stop() 方法
	if err := server.Stop(); err != nil {
		return err
	}

	// 从 map 中删除
	delete(fm.servers, proxy)
	return nil
}

// StopForward 停止指定端口的正向代理（兼容旧接口）
func (fm *ForwardManager) StopForward(serverPort uint16) error {
	fm.lock.Lock()
	defer fm.lock.Unlock()

	// 查找对应的服务器
	for proxy, server := range fm.servers {
		if proxy.UserPort == serverPort {
			if err := server.Stop(); err != nil {
				return err
			}
			delete(fm.servers, proxy)
			return nil
		}
	}
	return nil // 未找到对应服务，认为已停止
}

// Stop 停止正向代理服务
func (s *ForwardProxyServer) Stop() error {
	s.lock.Lock()
	if !s.running {
		s.lock.Unlock()
		return nil
	}
	s.running = false
	s.lock.Unlock()

	// 取消context，停止所有goroutine
	if s.cancel != nil {
		s.cancel()
	}

	// 关闭所有连接
	s.connectionManager.CloseAll()

	// 停止统计信息定期更新
	s.stats.stopPeriodicUpdate()

	// 更新代理状态和统计信息
	s.config.Status = 0 // 停止
	s.config.StoppedAt = time.Now()

	// 获取统计信息并保存到数据库
	totalConn, activeConn, bytesSent, bytesReceived, lastActivity := s.stats.GetStats()
	s.config.TotalConnections = totalConn
	s.config.ActiveConnections = int(activeConn)
	s.config.BytesTransferred = bytesSent
	s.config.BytesReceived = bytesReceived
	s.config.LastActivity = lastActivity

	// 🚀 最终保存统计信息到数据库
	updates := map[string]interface{}{
		"status":             s.config.Status,
		"stopped_at":         s.config.StoppedAt,
		"total_connections":  s.config.TotalConnections,
		"active_connections": s.config.ActiveConnections,
		"bytes_transferred":  s.config.BytesTransferred,
		"bytes_received":     s.config.BytesReceived,
		"last_activity":      s.config.LastActivity,
		"version":            s.config.Version + 1,
	}
	dbpool.ExecuteDBOperationAsyncAndWait("forward_proxy_final_stats", func(db *gorm.DB) error {
		return db.Model(s.config.Proxy).Updates(updates).Error
	})

	// 关闭用户监听器
	if s.userListener != nil {
		if err := s.userListener.Close(); err != nil {
			if global.LOG != nil {
				global.LOG.Error("关闭用户监听器失败", zap.Error(err))
			}
			return fmt.Errorf("关闭用户监听器失败: %v", err)
		}
	}

	if global.LOG != nil {
		global.LOG.Info("正向代理服务已停止", zap.Uint16("userPort", s.userPort))
	}

	return nil
}

// ===== 端口复用功能 =====

// CanReusePort 检查端口是否可以复用
func (fm *ForwardManager) CanReusePort(port uint16) bool {
	fm.portLock.RLock()
	defer fm.portLock.RUnlock()

	// 如果端口没有被使用，可以复用
	servers, exists := fm.portReuse[port]
	if !exists || len(servers) == 0 {
		return true
	}

	// 检查现有服务器是否都是正向代理（可以复用）
	for _, server := range servers {
		if server.config.Type != "forward" { // forward表示正向代理
			return false
		}
	}

	return true
}

// AddPortReuse 添加端口复用
func (fm *ForwardManager) AddPortReuse(port uint16, server *ForwardProxyServer) {
	fm.portLock.Lock()
	defer fm.portLock.Unlock()

	if fm.portReuse[port] == nil {
		fm.portReuse[port] = make([]*ForwardProxyServer, 0)
	}
	fm.portReuse[port] = append(fm.portReuse[port], server)
}

// RemovePortReuse 移除端口复用
func (fm *ForwardManager) RemovePortReuse(port uint16, server *ForwardProxyServer) {
	fm.portLock.Lock()
	defer fm.portLock.Unlock()

	servers, exists := fm.portReuse[port]
	if !exists {
		return
	}

	// 移除指定的服务器
	for i, s := range servers {
		if s == server {
			fm.portReuse[port] = append(servers[:i], servers[i+1:]...)
			break
		}
	}

	// 如果没有服务器了，删除端口记录
	if len(fm.portReuse[port]) == 0 {
		delete(fm.portReuse, port)
	}
}

// GetPortReuseInfo 获取端口复用信息
func (fm *ForwardManager) GetPortReuseInfo(port uint16) []string {
	fm.portLock.RLock()
	defer fm.portLock.RUnlock()

	servers, exists := fm.portReuse[port]
	if !exists {
		return nil
	}

	info := make([]string, 0, len(servers))
	for _, server := range servers {
		info = append(info, fmt.Sprintf("Client:%s:%d -> Target:%s:%d",
			server.clientAddr, server.clientPort,
			server.config.Name, server.config.ID))
	}

	return info
}

// StartServerWithPortReuse 启动支持端口复用的正向代理服务
func (fm *ForwardManager) StartServerWithPortReuse(proxy *basic.Proxy, allowReuse bool) error {
	if !allowReuse && !fm.CanReusePort(proxy.UserPort) {
		return fmt.Errorf("端口 %d 已被占用且不支持复用", proxy.UserPort)
	}

	// 使用现有的StartServer方法
	if err := fm.StartServer(proxy); err != nil {
		return err
	}

	// 添加到端口复用管理
	fm.lock.Lock()
	server, exists := fm.servers[proxy]
	fm.lock.Unlock()

	if exists {
		fm.AddPortReuse(proxy.UserPort, server)
		global.LOG.Info("正向代理已添加到端口复用",
			zap.Uint16("port", proxy.UserPort),
			zap.String("proxyID", proxy.ProxyID))
	}

	return nil
}
